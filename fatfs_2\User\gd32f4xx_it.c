/*!
    \file    gd32f4xx_it.c
    \brief   interrupt service routines
    
    \version 2020-09-04, V2.0.0, demo for GD32F4xx
*/

/*
    Copyright (c) 2020, GigaDevice Semiconductor Inc.

    Redistribution and use in source and binary forms, with or without modification, 
are permitted provided that the following conditions are met:

    1. Redistributions of source code must retain the above copyright notice, this 
       list of conditions and the following disclaimer.
    2. Redistributions in binary form must reproduce the above copyright notice, 
       this list of conditions and the following disclaimer in the documentation 
       and/or other materials provided with the distribution.
    3. Neither the name of the copyright holder nor the names of its contributors 
       may be used to endorse or promote products derived from this software without 
       specific prior written permission.

    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" 
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED 
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. 
IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, 
INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT 
NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR 
PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, 
WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) 
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY 
OF SUCH DAMAGE.
*/

#include "gd32f4xx_it.h"
#include "systick.h"
#include "sdcard.h"
#include "USART.h"

#define USART_RX_BUFFER_SIZE 64

// 外部变量声明
extern volatile char usart_rx_buffer[];
extern volatile uint8_t usart_rx_index;
extern volatile uint8_t usart_cmd_ready;

/*!
    \brief      this function handles NMI exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void NMI_Handler(void)
{
}

/*!
    \brief      this function handles HardFault exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void HardFault_Handler(void)
{
    /* if Hard Fault exception occurs, go to infinite loop */
    while (1){
    }
}

/*!
    \brief      this function handles MemManage exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void MemManage_Handler(void)
{
    /* if Memory Manage exception occurs, go to infinite loop */
    while (1){
    }
}

/*!
    \brief      this function handles BusFault exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void BusFault_Handler(void)
{
    /* if Bus Fault exception occurs, go to infinite loop */
    while (1){
    }
}

/*!
    \brief      this function handles UsageFault exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void UsageFault_Handler(void)
{
    /* if Usage Fault exception occurs, go to infinite loop */
    while (1){
    }
}

/*!
    \brief      this function handles SVC exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void SVC_Handler(void)
{
}

/*!
    \brief      this function handles DebugMon exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void DebugMon_Handler(void)
{
}

/*!
    \brief      this function handles PendSV exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void PendSV_Handler(void)
{
}

/*!
    \brief      this function handles SysTick exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void SysTick_Handler(void)
{
    delay_decrement();
}
/*!
    \brief      this function handles SDIO interrupt request
    \param[in]  none
    \param[out] none
    \retval     none
*/
void SDIO_IRQHandler(void)
{
    sd_interrupts_process();
}

/*!
    \brief      this function handles USART0 interrupt request
    \param[in]  none
    \param[out] none
    \retval     none
*/
void USART0_IRQHandler(void)
{
    if(usart_flag_get(USART0, USART_FLAG_RBNE) != RESET) {
        char ch = usart_data_receive(USART0); // 接收一个字符

        // 处理回车换行 - 命令结束标志
        if(ch == '\r' || ch == '\n') {
            if(usart_rx_index > 0) {
                // 命令输入完成，不回显回车换行
                usart_rx_buffer[usart_rx_index] = '\0'; // 确保字符串结束
                usart_cmd_ready = 1; // 设置命令就绪标志
            }
        }
        // 处理退格键
        else if(ch == '\b' || ch == 0x7F) {
            if(usart_rx_index > 0) {
                usart_rx_index--;
                usart_rx_buffer[usart_rx_index] = '\0';
                printf("\b \b"); // 回显退格：退格-空格-退格
            }
        }
        // 处理普通可打印字符
        else if(ch >= 0x20 && ch <= 0x7E) {
            // 回显字符
            printf("%c", ch);

            // 存储字符到数组
            if(usart_rx_index < (USART_RX_BUFFER_SIZE - 1)) {
                usart_rx_buffer[usart_rx_index++] = ch;
                usart_rx_buffer[usart_rx_index] = '\0'; // 保持字符串结束符
            } else {
                // 缓冲区溢出，重置
                usart_rx_index = 0;
                usart_rx_buffer[0] = '\0';
                printf("\r\nCommand too long. Enter command: ");
            }
        }

        // 清除中断标志
        usart_flag_clear(USART0, USART_FLAG_RBNE);
    }
}
