# 超阈值数据存储功能实现说明

## 功能概述
已成功实现超阈值数据存储功能，当采样电压超过设定的limit阈值时，自动将数据存储到TF卡的overLimit文件夹中。

## 功能特性

### 1. 超阈值检测
- 实时监控采样电压值
- 与配置的limit阈值进行比较
- 超限时LED2点亮，正常时LED2熄灭

### 2. 文件存储要求
**a. 文件数量控制：**
- 每个文件存储10条数据
- 超过10条后自动创建新文件

**b. 文件命名格式：**
- 文件名：`overLimit{datetime}.txt`
- datetime格式：连续14位数字（YYYYMMDDHHMMSS）
- 示例：`overLimit20250101003010.txt`（对应2025-01-01 00:30:10）

**c. 文件内容格式：**
```
2025-01-01 00:30:10 30V limit 10V
2025-01-01 00:30:13 30V limit 10V
2025-01-01 00:30:16 30V limit 10V
2025-01-01 11:15:00 30V limit 10V
2025-01-01 11:15:03 30V limit 10V
```

### 3. 存储路径
- 目录：`0:/overLimit/`
- 自动创建目录（如果不存在）

## 技术实现

### 核心函数

#### write_overlimit_data()
```c
void write_overlimit_data(char* time_str, float voltage, float limit)
```
- **功能：** 写入超限数据到overLimit文件夹
- **参数：**
  - `time_str`: 时间字符串（格式：2025-01-01 00:30:10）
  - `voltage`: 当前电压值
  - `limit`: 阈值限制
- **格式：** `时间 电压V limit 阈值V`

#### generate_overlimit_filename()
```c
void generate_overlimit_filename(char* filename)
```
- **功能：** 生成overLimit文件名
- **格式：** `0:/overLimit/overLimit{datetime}.txt`

### 数据格式转换
- **输入电压：** 浮点数（如30.5V）
- **输出格式：** 整数显示（如30V）
- **阈值显示：** 整数显示（如10V）

### 文件管理
- **计数器：** `file_manager.overlimit_count`
- **文件句柄：** `file_manager.overlimit_file`
- **自动切换：** 达到10条记录时自动创建新文件

## 集成说明

### 与采样系统集成
- 在每次采样时自动检测是否超限
- 超限时同时：
  1. 串口输出超限信息
  2. 点亮LED2指示灯
  3. 写入overLimit文件
  4. 写入sample文件（正常采样数据）

### 与其他存储功能的关系
- **独立存储：** overLimit数据独立于sample数据
- **并行写入：** 超限时同时写入sample和overLimit文件
- **统一管理：** 使用相同的文件管理器结构

## 调试功能
添加了详细的调试输出：
- 文件创建信息
- 数据写入确认
- 计数器状态
- 错误信息

## 使用示例

### 设置阈值
```bash
limit
Current limit: 10.00
Input new limit (0.00-500.00): 15.5
limit: 15.50
```

### 启动采样
```bash
start
Periodic Sampling
sample cycle: 5s
```

### 超限检测
当电压超过15.5V时：
- **串口输出：** `2025-01-01 12:00:05 ch0=20.0V OverLimit (15.50)!`
- **LED2：** 点亮
- **文件写入：** `2025-01-01 12:00:05 20V limit 16V`

## 文件结构示例
```
TF卡根目录/
├── sample/
│   └── sampleData20250101120005.txt
├── overLimit/
│   └── overLimit20250101120005.txt
├── log/
│   └── log1.txt
└── hidedata/
    └── hideData20250101120005.txt
```

## 验证方法

### 1. 目录检查
```bash
check dirs
=== Directory Status Check ===
overLimit directory: EXISTS
Storage enabled: YES
OverLimit count: 3
```

### 2. 功能测试
1. 设置较低的limit值（如5V）
2. 启动采样
3. 观察LED2状态
4. 检查overLimit文件夹中的文件
5. 验证文件内容格式

---
**实现完成时间：** 2025-06-16  
**状态：** ✅ 已实现并测试  
**符合要求：** ✅ 完全符合用户规格要求
