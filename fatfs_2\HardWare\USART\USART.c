#include "USART.h"

// 串口接收缓冲区和相关变量
#define USART_RX_BUFFER_SIZE 64
volatile char usart_rx_buffer[USART_RX_BUFFER_SIZE]; // 接收缓冲区
volatile uint8_t usart_rx_index = 0; // 接收索引
volatile uint8_t usart_cmd_ready = 0; // 命令就绪标志

void gd_eval_com_init(void)
{
    /* enable GPIO clock */
    rcu_periph_clock_enable(RCU_GPIOA);

    /* enable USART clock */
    rcu_periph_clock_enable(RCU_USART0);

    /* configure the USART0 TX pin and USART0 RX pin */
    gpio_af_set(GPIOA, GPIO_AF_7, GPIO_PIN_9);
    gpio_af_set(GPIOA, GPIO_AF_7, GPIO_PIN_10);

    /* configure USART0 TX as alternate function push-pull */
    gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_9);
    gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_9);

    /* configure USART0 RX as alternate function push-pull */
    gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_10);
    gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_10);

    /* USART configure */
    usart_deinit(USART0);
    usart_baudrate_set(USART0, 115200U);
    usart_receive_config(USART0, USART_RECEIVE_ENABLE);
    usart_transmit_config(USART0, USART_TRANSMIT_ENABLE);

    /* 启用USART接收中断 */
    usart_interrupt_enable(USART0, USART_INT_RBNE);

    usart_enable(USART0);
}


/* retarget the C library printf function to the USART */
int fputc(int ch, FILE *f)
{
    usart_data_transmit(USART0, (uint8_t)ch);
    while(RESET == usart_flag_get(USART0, USART_FLAG_TBE));
    return ch;
}

// 获取接收到的命令
uint8_t usart_get_command(char* cmd_buffer, uint8_t max_len)
{
    if(usart_cmd_ready) {
        uint8_t len = usart_rx_index;
        if(len > 0 && len < max_len) {
            // 复制命令到缓冲区
            for(uint8_t i = 0; i < len; i++) {
                cmd_buffer[i] = usart_rx_buffer[i];
            }
            cmd_buffer[len] = '\0';

            // 重置接收状态
            usart_rx_index = 0;
            usart_cmd_ready = 0;

            return len;
        } else {
            // 命令长度异常，重置
            usart_rx_index = 0;
            usart_cmd_ready = 0;
            return 0;
        }
    }
    return 0;
}

// 清除当前接收的字符
void usart_clear_buffer(void)
{
    usart_rx_index = 0;
    usart_cmd_ready = 0;
}

// 检查是否有命令就绪
uint8_t usart_is_command_ready(void)
{
    return usart_cmd_ready;
}


