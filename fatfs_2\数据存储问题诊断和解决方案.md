# 数据存储问题诊断和解决方案

## 问题描述
用户反馈只有log文件夹里面有数据，其他文件夹（sample、overLimit、hideData）都没有数据。

## 问题分析

### 可能的原因
1. **存储功能被禁用** - `file_manager.storage_enabled = 0`
2. **采样条件不满足** - 采样时间条件判断有问题
3. **文件创建失败** - TF卡权限或空间问题
4. **采样函数未被正确调用** - 主循环中的调用有问题
5. **目录不存在** - 相关目录未正确创建

### 诊断步骤

#### 1. 检查存储状态
```bash
check dirs
```
应该显示：
- Storage enabled: YES
- 各目录状态: EXISTS

#### 2. 测试采样功能
```bash
test sample
```
这个新增的命令会：
- 显示当前存储状态
- 强制执行一次采样
- 显示详细的调试信息

#### 3. 检查采样条件
采样触发条件：`current_time.second % sample_cycle == 0`
- 默认采样周期：5秒
- 触发时间：0、5、10、15、20、25、30、35、40、45、50、55秒

## 已实施的修复措施

### 1. 添加调试信息
在关键位置添加了详细的调试输出：

**采样触发时：**
```c
printf("=== Sampling triggered at %02d:%02d:%02d ===\r\n", 
       current_time.hour, current_time.minute, current_time.second);
```

**存储状态检查：**
```c
printf("Storage enabled: %s, hide_mode: %d\r\n", 
       file_manager.storage_enabled ? "YES" : "NO", hide_mode);
```

**文件操作：**
- 文件创建确认
- 数据写入确认
- 计数器状态
- 错误信息

### 2. 新增测试命令
添加了 `test sample` 命令，可以：
- 检查存储状态
- 强制执行一次采样
- 验证文件写入功能
- 显示计数器状态

### 3. 修复目录名称
确保所有目录名称符合用户要求：
- ✅ `sample` 文件夹
- ✅ `overLimit` 文件夹（修正了大小写）
- ✅ `hideData` 文件夹（修正了大小写）
- ✅ `log` 文件夹

### 4. 优化存储逻辑
**正常模式：**
- sample文件夹：存储正常采样数据
- overLimit文件夹：存储超限数据

**hide模式：**
- hideData文件夹：存储未加密+加密数据
- sample文件夹：不存储数据
- overLimit文件夹：仍存储超限数据

## 测试步骤

### 1. 基础功能测试
```bash
# 检查系统状态
test

# 检查目录状态
check dirs

# 创建目录（如果需要）
create dirs
```

### 2. 采样功能测试
```bash
# 测试采样功能
test sample

# 启动正常采样
start

# 等待几个采样周期，观察调试输出

# 停止采样
stop
```

### 3. hide模式测试
```bash
# 启动采样
start

# 启用hide模式
hide

# 观察数据格式变化

# 禁用hide模式
unhide

# 停止采样
stop
```

### 4. 超限测试
```bash
# 设置较低的limit值
limit
Input value(0~500): 5

# 启动采样
start

# 观察是否有超限数据生成
```

## 预期结果

### 正常模式下
- **sample文件夹：** 包含 `sampleData{datetime}.txt` 文件
- **overLimit文件夹：** 超限时包含 `overLimit{datetime}.txt` 文件
- **log文件夹：** 包含 `log{id}.txt` 文件

### hide模式下
- **hideData文件夹：** 包含 `hideData{datetime}.txt` 文件
- **sample文件夹：** 空（不存储数据）
- **overLimit文件夹：** 超限时包含 `overLimit{datetime}.txt` 文件
- **log文件夹：** 包含 `log{id}.txt` 文件

## 文件格式验证

### sample文件格式
```
2025-01-01 00:30:10 1.5V
2025-01-01 00:30:15 1.6V
2025-01-01 00:30:20 1.7V
```

### overLimit文件格式
```
2025-01-01 00:30:10 30V limit 10V
2025-01-01 00:30:15 31V limit 10V
```

### hideData文件格式
```
2025-01-01 00:30:10 1.5V
hide: XXXXXXXXXXXXXXXX
2025-01-01 00:30:15 1.6V
hide: XXXXXXXXXXXXXXXX
```

## 故障排除

### 如果仍然没有数据
1. **检查TF卡状态：** 确保TF卡正常工作
2. **检查存储空间：** 确保TF卡有足够空间
3. **检查采样状态：** 确保采样已启动
4. **检查时间设置：** 确保RTC时间正确
5. **查看调试输出：** 观察详细的调试信息

### 常见问题
1. **采样不触发：** 检查采样周期设置和时间条件
2. **文件创建失败：** 检查TF卡状态和目录权限
3. **数据格式错误：** 检查时间格式和电压读取
4. **计数器不增加：** 检查文件写入是否成功

## 下一步行动
1. 编译并烧录修改后的代码
2. 使用 `test sample` 命令进行初步测试
3. 启动正常采样并观察调试输出
4. 检查TF卡中的文件生成情况
5. 根据调试信息进一步优化

---
**修复完成时间：** 2025-06-16  
**状态：** ✅ 已添加调试功能和测试命令  
**下一步：** 需要实际测试验证修复效果
