# 系统启动流程实现说明

## 功能概述

按照要求实现了系统上电（复位）后的标准化启动流程，包括串口打印信息和OLED显示控制。

## 实现的启动流程

### 1.1 系统初始化提示
- **时机**: 系统上电后立即执行
- **输出**: `====system init====`
- **位置**: UsrFunction函数开始处

### 1.2 设备ID读取和显示
- **时机**: Flash初始化和设备ID加载完成后
- **输出格式**: `flash_id: Device_ID:2025-CIMC-2025247961`
- **数据源**: 从Flash中读取的设备ID数据

### 1.3 系统就绪提示
- **时机**: 所有硬件初始化和TF卡初始化完成后
- **输出**: `====system ready====`
- **位置**: 在进入命令循环之前

### 1.4 OLED显示设置
- **显示内容**: "system idle"
- **显示位置**: OLED第一行
- **时机**: 系统就绪后立即显示

## 技术实现

### 代码结构
```c
void UsrFunction(void)
{
    // 1.1 系统上电后打印初始化信息
    printf("====system init====\r\n");
    
    // 硬件初始化...
    nvic_config();
    LED_Init();
    gd_eval_com_init();
    spi_flash_init();
    ADC_port_init();
    KEY_Init();
    local_rtc_init();
    
    // 数据加载
    load_config_from_flash();
    load_device_id_from_flash();
    
    // 1.2 从flash读取设备ID并打印
    printf("flash_id: %s\r\n", device_id_data.device_id);
    
    // 其他初始化...
    increment_boot_count();
    sample_cycle = config_data.cycle;
    
    // TF卡初始化...
    
    // 1.3 打印系统就绪信息
    printf("====system ready====\r\n");
    
    // 1.4 OLED第一行显示"system idle"
    OLED_Init();
    OLED_Clear();
    OLED_ShowString(0, 0, (unsigned char*)"system idle", 12);
    OLED_Refresh();
    
    // 进入主循环...
}
```

### 关键特性

1. **顺序执行**: 严格按照1.1 -> 1.2 -> 1.3 -> 1.4的顺序执行
2. **数据完整性**: 确保设备ID从Flash正确读取后再显示
3. **显示同步**: OLED显示在系统完全就绪后设置
4. **格式标准化**: 所有输出格式严格按照要求实现

## 启动时序

```
系统上电/复位
    ↓
====system init====
    ↓
硬件初始化 (LED, USART, SPI Flash, ADC, KEY, RTC)
    ↓
数据加载 (配置数据, 设备ID)
    ↓
flash_id: Device_ID:2025-CIMC-2025247961
    ↓
上电次数增加
    ↓
TF卡初始化
    ↓
====system ready====
    ↓
OLED显示: "system idle"
    ↓
进入主循环 (命令处理)
```

## 输出示例

系统启动时的完整串口输出：
```
====system init====
Boot count: 1
SD Card disk_initialize:0
SD Card f_mount:0

SD Card Initialize Success!
flash_id: Device_ID:2025-CIMC-2025247961
====system ready====
System ready. Enter commands:
- 'test': System self-check
- 'device id': Show device ID information
...
Enter command: 
```

## 验证方法

1. **串口监控**: 通过串口工具监控启动输出
2. **OLED检查**: 确认OLED第一行显示"system idle"
3. **时序验证**: 确认各阶段按正确顺序执行
4. **设备ID验证**: 确认显示的设备ID正确

## 注意事项

1. **初始化顺序**: 必须先初始化串口才能正常打印信息
2. **Flash读取**: 设备ID显示前必须确保Flash初始化完成
3. **OLED时机**: OLED显示设置在系统完全就绪后执行
4. **格式一致性**: 严格按照要求的输出格式实现

## 相关文件

- `fatfs_2\Function\Function.c` - 主要实现文件
- `fatfs_2\HardWare\OLED.c` - OLED显示控制
- `fatfs_2\HardWare\USART\USART.c` - 串口通信
