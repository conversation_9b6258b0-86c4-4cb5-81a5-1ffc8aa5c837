Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    gd32f4xx_it.o(i.SDIO_IRQHandler) refers to sdcard.o(i.sd_interrupts_process) for sd_interrupts_process
    gd32f4xx_it.o(i.SysTick_Handler) refers to systick.o(i.delay_decrement) for delay_decrement
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_usart.o(i.usart_data_receive) for usart_data_receive
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to printfa.o(i.__0printf) for __2printf
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_usart.o(i.usart_flag_clear) for usart_flag_clear
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to usart.o(.data) for usart_rx_index
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to usart.o(.bss) for usart_rx_buffer
    main.o(i.main) refers to function.o(i.System_Init) for System_Init
    main.o(i.main) refers to function.o(i.UsrFunction) for UsrFunction
    systick.o(i.delay_1ms) refers to systick.o(.data) for delay
    systick.o(i.delay_decrement) refers to systick.o(.data) for delay
    systick.o(i.systick_config) refers to systick.o(i.NVIC_SetPriority) for NVIC_SetPriority
    systick.o(i.systick_config) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    led.o(i.LED_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    led.o(i.LED_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    led.o(i.LED_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    led.o(i.LED_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    usart.o(i.fputc) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    usart.o(i.fputc) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    usart.o(i.gd_eval_com_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    usart.o(i.gd_eval_com_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    usart.o(i.gd_eval_com_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    usart.o(i.gd_eval_com_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    usart.o(i.gd_eval_com_init) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    usart.o(i.gd_eval_com_init) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    usart.o(i.gd_eval_com_init) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    usart.o(i.gd_eval_com_init) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    usart.o(i.gd_eval_com_init) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    usart.o(i.gd_eval_com_init) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    usart.o(i.usart_clear_buffer) refers to usart.o(.data) for usart_rx_index
    usart.o(i.usart_get_command) refers to usart.o(.data) for usart_cmd_ready
    usart.o(i.usart_get_command) refers to usart.o(.bss) for usart_rx_buffer
    usart.o(i.usart_is_command_ready) refers to usart.o(.data) for usart_cmd_ready
    sdcard.o(i.cmdsent_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdcard.o(i.cmdsent_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_multi_data_mode_init) for dma_multi_data_mode_init
    sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_flow_controller_config) for dma_flow_controller_config
    sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_multi_data_mode_init) for dma_multi_data_mode_init
    sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_flow_controller_config) for dma_flow_controller_config
    sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    sdcard.o(i.gpio_config) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    sdcard.o(i.gpio_config) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    sdcard.o(i.gpio_config) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    sdcard.o(i.r1_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.r1_error_check) refers to gd32f4xx_sdio.o(i.sdio_command_index_get) for sdio_command_index_get
    sdcard.o(i.r1_error_check) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.r1_error_check) refers to sdcard.o(i.r1_error_type_check) for r1_error_type_check
    sdcard.o(i.r2_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.r3_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.r6_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.r6_error_check) refers to gd32f4xx_sdio.o(i.sdio_command_index_get) for sdio_command_index_get
    sdcard.o(i.r6_error_check) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.r7_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.rcu_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_block_read) refers to sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_block_read) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdcard.o(i.sd_block_read) refers to sdcard.o(i.dma_receive_config) for dma_receive_config
    sdcard.o(i.sd_block_read) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdcard.o(i.sd_block_read) refers to sdcard.o(.data) for transerror
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_block_write) refers to sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_block_write) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_data_write) for sdio_data_write
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdcard.o(i.sd_block_write) refers to sdcard.o(i.dma_transfer_config) for dma_transfer_config
    sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdcard.o(i.sd_block_write) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdcard.o(i.sd_block_write) refers to sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdcard.o(i.sd_block_write) refers to sdcard.o(.data) for transerror
    sdcard.o(i.sd_bus_mode_config) refers to sdcard.o(i.sd_bus_width_config) for sd_bus_width_config
    sdcard.o(i.sd_bus_mode_config) refers to gd32f4xx_sdio.o(i.sdio_clock_config) for sdio_clock_config
    sdcard.o(i.sd_bus_mode_config) refers to gd32f4xx_sdio.o(i.sdio_bus_mode_set) for sdio_bus_mode_set
    sdcard.o(i.sd_bus_mode_config) refers to gd32f4xx_sdio.o(i.sdio_hardware_clock_disable) for sdio_hardware_clock_disable
    sdcard.o(i.sd_bus_mode_config) refers to sdcard.o(.data) for cardtype
    sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_bus_width_config) refers to sdcard.o(i.sd_scr_get) for sd_scr_get
    sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_bus_width_config) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_bus_width_config) refers to sdcard.o(.data) for sd_scr
    sdcard.o(i.sd_card_capacity_get) refers to sdcard.o(.data) for cardtype
    sdcard.o(i.sd_card_capacity_get) refers to sdcard.o(.bss) for sd_csd
    sdcard.o(i.sd_card_information_get) refers to sdcard.o(.data) for cardtype
    sdcard.o(i.sd_card_information_get) refers to sdcard.o(.bss) for sd_cid
    sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_power_state_get) for sdio_power_state_get
    sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_card_init) refers to sdcard.o(i.r2_error_check) for r2_error_check
    sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_card_init) refers to sdcard.o(i.r6_error_check) for r6_error_check
    sdcard.o(i.sd_card_init) refers to sdcard.o(.data) for cardtype
    sdcard.o(i.sd_card_init) refers to sdcard.o(.bss) for sd_cid
    sdcard.o(i.sd_card_select_deselect) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_card_select_deselect) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_card_select_deselect) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_card_select_deselect) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_command_index_get) for sdio_command_index_get
    sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_card_state_get) refers to sdcard.o(i.r1_error_type_check) for r1_error_type_check
    sdcard.o(i.sd_card_state_get) refers to sdcard.o(.data) for sd_rca
    sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_cardstatus_get) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_cardstatus_get) refers to sdcard.o(.data) for sd_rca
    sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_erase) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_erase) refers to sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdcard.o(i.sd_erase) refers to sdcard.o(.bss) for sd_csd
    sdcard.o(i.sd_erase) refers to sdcard.o(.data) for cardtype
    sdcard.o(i.sd_init) refers to sdcard.o(i.rcu_config) for rcu_config
    sdcard.o(i.sd_init) refers to sdcard.o(i.gpio_config) for gpio_config
    sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_deinit) for sdio_deinit
    sdcard.o(i.sd_init) refers to sdcard.o(i.sd_power_on) for sd_power_on
    sdcard.o(i.sd_init) refers to sdcard.o(i.sd_card_init) for sd_card_init
    sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_clock_config) for sdio_clock_config
    sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_bus_mode_set) for sdio_bus_mode_set
    sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_hardware_clock_disable) for sdio_hardware_clock_disable
    sdcard.o(i.sd_interrupts_process) refers to gd32f4xx_sdio.o(i.sdio_interrupt_flag_get) for sdio_interrupt_flag_get
    sdcard.o(i.sd_interrupts_process) refers to sdcard.o(i.sd_transfer_stop) for sd_transfer_stop
    sdcard.o(i.sd_interrupts_process) refers to gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear) for sdio_interrupt_flag_clear
    sdcard.o(i.sd_interrupts_process) refers to gd32f4xx_sdio.o(i.sdio_interrupt_disable) for sdio_interrupt_disable
    sdcard.o(i.sd_interrupts_process) refers to sdcard.o(.data) for transerror
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_lock_unlock) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_data_write) for sdio_data_write
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.sd_lock_unlock) refers to sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdcard.o(i.sd_lock_unlock) refers to sdcard.o(.bss) for sd_csd
    sdcard.o(i.sd_lock_unlock) refers to sdcard.o(.data) for sd_rca
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_multiblocks_read) refers to sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_multiblocks_read) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdcard.o(i.sd_multiblocks_read) refers to sdcard.o(i.dma_receive_config) for dma_receive_config
    sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdcard.o(i.sd_multiblocks_read) refers to sdcard.o(.data) for transerror
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_multiblocks_write) refers to sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_multiblocks_write) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_data_write) for sdio_data_write
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdcard.o(i.sd_multiblocks_write) refers to sdcard.o(i.dma_transfer_config) for dma_transfer_config
    sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdcard.o(i.sd_multiblocks_write) refers to sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdcard.o(i.sd_multiblocks_write) refers to sdcard.o(.data) for transerror
    sdcard.o(i.sd_power_off) refers to gd32f4xx_sdio.o(i.sdio_power_state_set) for sdio_power_state_set
    sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_clock_config) for sdio_clock_config
    sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_bus_mode_set) for sdio_bus_mode_set
    sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_hardware_clock_disable) for sdio_hardware_clock_disable
    sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_power_state_set) for sdio_power_state_set
    sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_clock_enable) for sdio_clock_enable
    sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_power_on) refers to sdcard.o(i.cmdsent_error_check) for cmdsent_error_check
    sdcard.o(i.sd_power_on) refers to sdcard.o(i.r7_error_check) for r7_error_check
    sdcard.o(i.sd_power_on) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_power_on) refers to sdcard.o(i.r3_error_check) for r3_error_check
    sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_power_on) refers to sdcard.o(.data) for cardtype
    sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_scr_get) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_sdstatus_get) refers to sdcard.o(i.r1_error_check) for r1_error_check
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdcard.o(i.sd_sdstatus_get) refers to sdcard.o(.data) for sd_rca
    sdcard.o(i.sd_transfer_mode_config) refers to sdcard.o(.data) for transmode
    sdcard.o(i.sd_transfer_state_get) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdcard.o(i.sd_transfer_stop) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdcard.o(i.sd_transfer_stop) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdcard.o(i.sd_transfer_stop) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdcard.o(i.sd_transfer_stop) refers to sdcard.o(i.r1_error_check) for r1_error_check
    spi_flash.o(i.spi_flash_buffer_read) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    spi_flash.o(i.spi_flash_buffer_read) refers to spi_flash.o(i.spi_flash_send_byte) for spi_flash_send_byte
    spi_flash.o(i.spi_flash_buffer_read) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    spi_flash.o(i.spi_flash_buffer_write) refers to spi_flash.o(i.spi_flash_page_write) for spi_flash_page_write
    spi_flash.o(i.spi_flash_bulk_erase) refers to spi_flash.o(i.spi_flash_write_enable) for spi_flash_write_enable
    spi_flash.o(i.spi_flash_bulk_erase) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    spi_flash.o(i.spi_flash_bulk_erase) refers to spi_flash.o(i.spi_flash_send_byte) for spi_flash_send_byte
    spi_flash.o(i.spi_flash_bulk_erase) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    spi_flash.o(i.spi_flash_bulk_erase) refers to spi_flash.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    spi_flash.o(i.spi_flash_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    spi_flash.o(i.spi_flash_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    spi_flash.o(i.spi_flash_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    spi_flash.o(i.spi_flash_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    spi_flash.o(i.spi_flash_init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    spi_flash.o(i.spi_flash_init) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    spi_flash.o(i.spi_flash_init) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    spi_flash.o(i.spi_flash_page_write) refers to spi_flash.o(i.spi_flash_write_enable) for spi_flash_write_enable
    spi_flash.o(i.spi_flash_page_write) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    spi_flash.o(i.spi_flash_page_write) refers to spi_flash.o(i.spi_flash_send_byte) for spi_flash_send_byte
    spi_flash.o(i.spi_flash_page_write) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    spi_flash.o(i.spi_flash_page_write) refers to spi_flash.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    spi_flash.o(i.spi_flash_read_byte) refers to spi_flash.o(i.spi_flash_send_byte) for spi_flash_send_byte
    spi_flash.o(i.spi_flash_read_id) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    spi_flash.o(i.spi_flash_read_id) refers to spi_flash.o(i.spi_flash_send_byte) for spi_flash_send_byte
    spi_flash.o(i.spi_flash_read_id) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    spi_flash.o(i.spi_flash_sector_erase) refers to spi_flash.o(i.spi_flash_write_enable) for spi_flash_write_enable
    spi_flash.o(i.spi_flash_sector_erase) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    spi_flash.o(i.spi_flash_sector_erase) refers to spi_flash.o(i.spi_flash_send_byte) for spi_flash_send_byte
    spi_flash.o(i.spi_flash_sector_erase) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    spi_flash.o(i.spi_flash_sector_erase) refers to spi_flash.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    spi_flash.o(i.spi_flash_send_byte) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    spi_flash.o(i.spi_flash_send_byte) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    spi_flash.o(i.spi_flash_send_byte) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    spi_flash.o(i.spi_flash_send_halfword) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    spi_flash.o(i.spi_flash_send_halfword) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    spi_flash.o(i.spi_flash_send_halfword) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    spi_flash.o(i.spi_flash_start_read_sequence) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    spi_flash.o(i.spi_flash_start_read_sequence) refers to spi_flash.o(i.spi_flash_send_byte) for spi_flash_send_byte
    spi_flash.o(i.spi_flash_wait_for_write_end) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    spi_flash.o(i.spi_flash_wait_for_write_end) refers to spi_flash.o(i.spi_flash_send_byte) for spi_flash_send_byte
    spi_flash.o(i.spi_flash_wait_for_write_end) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    spi_flash.o(i.spi_flash_write_enable) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    spi_flash.o(i.spi_flash_write_enable) refers to spi_flash.o(i.spi_flash_send_byte) for spi_flash_send_byte
    spi_flash.o(i.spi_flash_write_enable) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    adc.o(i.ADC_Init) refers to gd32f4xx_adc.o(i.adc_deinit) for adc_deinit
    adc.o(i.ADC_Init) refers to gd32f4xx_adc.o(i.adc_special_function_config) for adc_special_function_config
    adc.o(i.ADC_Init) refers to gd32f4xx_adc.o(i.adc_data_alignment_config) for adc_data_alignment_config
    adc.o(i.ADC_Init) refers to gd32f4xx_adc.o(i.adc_channel_length_config) for adc_channel_length_config
    adc.o(i.ADC_Init) refers to gd32f4xx_adc.o(i.adc_routine_channel_config) for adc_routine_channel_config
    adc.o(i.ADC_Init) refers to gd32f4xx_adc.o(i.adc_external_trigger_source_config) for adc_external_trigger_source_config
    adc.o(i.ADC_Init) refers to gd32f4xx_adc.o(i.adc_external_trigger_config) for adc_external_trigger_config
    adc.o(i.ADC_Init) refers to gd32f4xx_adc.o(i.adc_enable) for adc_enable
    adc.o(i.ADC_Init) refers to systick.o(i.delay_1ms) for delay_1ms
    adc.o(i.ADC_Init) refers to gd32f4xx_adc.o(i.adc_calibration_enable) for adc_calibration_enable
    adc.o(i.ADC_Read_Value) refers to gd32f4xx_adc.o(i.adc_software_trigger_enable) for adc_software_trigger_enable
    adc.o(i.ADC_Read_Value) refers to gd32f4xx_adc.o(i.adc_flag_get) for adc_flag_get
    adc.o(i.ADC_Read_Value) refers to gd32f4xx_adc.o(i.adc_routine_data_read) for adc_routine_data_read
    adc.o(i.ADC_Read_Value) refers to gd32f4xx_adc.o(i.adc_flag_clear) for adc_flag_clear
    adc.o(i.ADC_Read_Voltage) refers to adc.o(i.ADC_Read_Value) for ADC_Read_Value
    adc.o(i.ADC_port_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    adc.o(i.ADC_port_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    adc.o(i.ADC_port_init) refers to gd32f4xx_adc.o(i.adc_clock_config) for adc_clock_config
    adc.o(i.ADC_port_init) refers to adc.o(i.ADC_Init) for ADC_Init
    adc.o(i.ADC_port_init) refers to gd32f4xx_adc.o(i.adc_software_trigger_enable) for adc_software_trigger_enable
    key.o(i.KEY_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    key.o(i.KEY_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    key.o(i.KEY_Stat) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    key.o(i.KEY_Stat) refers to systick.o(i.delay_1ms) for delay_1ms
    oled.o(i.I2C_Start) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    oled.o(i.I2C_Start) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    oled.o(i.I2C_Start) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    oled.o(i.I2C_Start) refers to oled.o(i.IIC_delay) for IIC_delay
    oled.o(i.I2C_Start) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    oled.o(i.I2C_Stop) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    oled.o(i.I2C_Stop) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    oled.o(i.I2C_Stop) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    oled.o(i.I2C_Stop) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    oled.o(i.I2C_Stop) refers to oled.o(i.IIC_delay) for IIC_delay
    oled.o(i.I2C_WaitAck) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    oled.o(i.I2C_WaitAck) refers to oled.o(i.IIC_delay) for IIC_delay
    oled.o(i.I2C_WaitAck) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    oled.o(i.I2C_WaitAck) refers to oled.o(i.I2C_Stop) for I2C_Stop
    oled.o(i.I2C_WaitAck) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    oled.o(i.I2C_WaitAck) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    oled.o(i.I2C_WaitAck) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    oled.o(i.OLED_Clear) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_ClearPoint) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_ColorTurn) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisPlay_Off) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisPlay_On) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisplayTurn) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DrawCircle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawLine) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawPoint) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    oled.o(i.OLED_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    oled.o(i.OLED_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    oled.o(i.OLED_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    oled.o(i.OLED_Init) refers to systick.o(i.delay_1ms) for delay_1ms
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Refresh) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Refresh) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_ScrollDisplay) refers to oled.o(i.OLED_ShowChinese) for OLED_ShowChinese
    oled.o(i.OLED_ScrollDisplay) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    oled.o(i.OLED_ScrollDisplay) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_ClearPoint) for OLED_ClearPoint
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for asc2_1206
    oled.o(i.OLED_ShowChinese) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_ShowChinese) refers to oled.o(i.OLED_ClearPoint) for OLED_ClearPoint
    oled.o(i.OLED_ShowChinese) refers to oled.o(.data) for Hzk1
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowPicture) refers to oled.o(i.OLED_WR_BP) for OLED_WR_BP
    oled.o(i.OLED_ShowPicture) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_WR_BP) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.I2C_Start) for I2C_Start
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.Send_Byte) for Send_Byte
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.I2C_WaitAck) for I2C_WaitAck
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.I2C_Stop) for I2C_Stop
    oled.o(i.Send_Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    oled.o(i.Send_Byte) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    oled.o(i.Send_Byte) refers to oled.o(i.IIC_delay) for IIC_delay
    function.o(i.System_Init) refers to systick.o(i.systick_config) for systick_config
    function.o(i.UsrFunction) refers to function.o(i.nvic_config) for nvic_config
    function.o(i.UsrFunction) refers to led.o(i.LED_Init) for LED_Init
    function.o(i.UsrFunction) refers to usart.o(i.gd_eval_com_init) for gd_eval_com_init
    function.o(i.UsrFunction) refers to spi_flash.o(i.spi_flash_init) for spi_flash_init
    function.o(i.UsrFunction) refers to adc.o(i.ADC_port_init) for ADC_port_init
    function.o(i.UsrFunction) refers to key.o(i.KEY_Init) for KEY_Init
    function.o(i.UsrFunction) refers to oled.o(i.OLED_Init) for OLED_Init
    function.o(i.UsrFunction) refers to oled.o(i.OLED_Clear) for OLED_Clear
    function.o(i.UsrFunction) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    function.o(i.UsrFunction) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    function.o(i.UsrFunction) refers to function.o(i.local_rtc_init) for local_rtc_init
    function.o(i.UsrFunction) refers to function.o(i.load_config_from_flash) for load_config_from_flash
    function.o(i.UsrFunction) refers to function.o(i.increment_boot_count) for increment_boot_count
    function.o(i.UsrFunction) refers to diskio.o(i.disk_initialize) for disk_initialize
    function.o(i.UsrFunction) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.UsrFunction) refers to ff.o(i.f_mount) for f_mount
    function.o(i.UsrFunction) refers to function.o(i.init_data_storage) for init_data_storage
    function.o(i.UsrFunction) refers to memseta.o(.text) for __aeabi_memclr4
    function.o(i.UsrFunction) refers to usart.o(i.usart_is_command_ready) for usart_is_command_ready
    function.o(i.UsrFunction) refers to usart.o(i.usart_get_command) for usart_get_command
    function.o(i.UsrFunction) refers to strcmp.o(.text) for strcmp
    function.o(i.UsrFunction) refers to function.o(i.system_self_check) for system_self_check
    function.o(i.UsrFunction) refers to function.o(i.rtc_config_command) for rtc_config_command
    function.o(i.UsrFunction) refers to function.o(i.rtc_now_command) for rtc_now_command
    function.o(i.UsrFunction) refers to function.o(i.read_config_from_tf) for read_config_from_tf
    function.o(i.UsrFunction) refers to function.o(i.set_ratio_value) for set_ratio_value
    function.o(i.UsrFunction) refers to function.o(i.set_limit_value) for set_limit_value
    function.o(i.UsrFunction) refers to function.o(i.config_save_command) for config_save_command
    function.o(i.UsrFunction) refers to function.o(i.config_read_command) for config_read_command
    function.o(i.UsrFunction) refers to function.o(i.start_sampling) for start_sampling
    function.o(i.UsrFunction) refers to function.o(i.stop_sampling) for stop_sampling
    function.o(i.UsrFunction) refers to function.o(i.hide_command) for hide_command
    function.o(i.UsrFunction) refers to function.o(i.unhide_command) for unhide_command
    function.o(i.UsrFunction) refers to function.o(.data) for config_data
    function.o(i.UsrFunction) refers to function.o(.bss) for fs
    function.o(i.UsrFunction) refers to function.o(i.check_directories_status) for check_directories_status
    function.o(i.UsrFunction) refers to function.o(i.create_directories) for create_directories
    function.o(i.UsrFunction) refers to function.o(i.check_key1_press) for check_key1_press
    function.o(i.UsrFunction) refers to function.o(i.check_key234_press) for check_key234_press
    function.o(i.UsrFunction) refers to function.o(i.update_sampling) for update_sampling
    function.o(i.UsrFunction) refers to gd32f4xx_gpio.o(i.gpio_bit_toggle) for gpio_bit_toggle
    function.o(i.UsrFunction) refers to function.o(.conststring) for .conststring
    function.o(i.adjust_sample_cycle) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.adjust_sample_cycle) refers to function.o(i.save_config_to_flash) for save_config_to_flash
    function.o(i.adjust_sample_cycle) refers to function.o(i.update_oled_display) for update_oled_display
    function.o(i.adjust_sample_cycle) refers to function.o(.data) for sample_cycle
    function.o(i.check_and_create_new_file) refers to ff.o(i.f_close) for f_close
    function.o(i.check_and_create_new_file) refers to ff.o(i.f_mkdir) for f_mkdir
    function.o(i.check_and_create_new_file) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.check_and_create_new_file) refers to function.o(i.generate_sample_filename) for generate_sample_filename
    function.o(i.check_and_create_new_file) refers to ff.o(i.f_open) for f_open
    function.o(i.check_and_create_new_file) refers to function.o(i.generate_hide_filename) for generate_hide_filename
    function.o(i.check_and_create_new_file) refers to function.o(i.generate_overlimit_filename) for generate_overlimit_filename
    function.o(i.check_and_create_new_file) refers to function.o(.bss) for file_manager
    function.o(i.check_command_match) refers to strncmp.o(.text) for strncmp
    function.o(i.check_command_match) refers to function.o(i.system_self_check) for system_self_check
    function.o(i.check_command_match) refers to function.o(i.rtc_config_command) for rtc_config_command
    function.o(i.check_command_match) refers to function.o(i.rtc_now_command) for rtc_now_command
    function.o(i.check_command_match) refers to function.o(i.read_config_from_tf) for read_config_from_tf
    function.o(i.check_command_match) refers to function.o(i.set_ratio_value) for set_ratio_value
    function.o(i.check_command_match) refers to function.o(i.set_limit_value) for set_limit_value
    function.o(i.check_command_match) refers to function.o(i.config_save_command) for config_save_command
    function.o(i.check_command_match) refers to function.o(i.config_read_command) for config_read_command
    function.o(i.check_command_match) refers to function.o(i.start_sampling) for start_sampling
    function.o(i.check_command_match) refers to function.o(i.stop_sampling) for stop_sampling
    function.o(i.check_command_match) refers to function.o(i.hide_command) for hide_command
    function.o(i.check_command_match) refers to function.o(i.unhide_command) for unhide_command
    function.o(i.check_directories_status) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.check_directories_status) refers to ff.o(i.f_opendir) for f_opendir
    function.o(i.check_directories_status) refers to function.o(.bss) for file_manager
    function.o(i.check_key1_press) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    function.o(i.check_key1_press) refers to function.o(i.toggle_sampling_by_key) for toggle_sampling_by_key
    function.o(i.check_key1_press) refers to function.o(.data) for key1_last_state
    function.o(i.check_key234_press) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    function.o(i.check_key234_press) refers to function.o(i.adjust_sample_cycle) for adjust_sample_cycle
    function.o(i.check_key234_press) refers to function.o(.data) for key2_last_state
    function.o(i.config_read_command) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.config_read_command) refers to function.o(i.load_config_from_flash) for load_config_from_flash
    function.o(i.config_read_command) refers to f2d.o(.text) for __aeabi_f2d
    function.o(i.config_read_command) refers to function.o(.data) for config_data
    function.o(i.config_save_command) refers to f2d.o(.text) for __aeabi_f2d
    function.o(i.config_save_command) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.config_save_command) refers to function.o(i.save_config_to_flash) for save_config_to_flash
    function.o(i.config_save_command) refers to function.o(i.write_log_data) for write_log_data
    function.o(i.config_save_command) refers to function.o(.data) for config_data
    function.o(i.create_directories) refers to ff.o(i.f_mkdir) for f_mkdir
    function.o(i.create_directories) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.encode_voltage_hex) refers to printfa.o(i.__0sprintf) for __2sprintf
    function.o(i.flash_self_check) refers to spi_flash.o(i.spi_flash_read_id) for spi_flash_read_id
    function.o(i.flash_self_check) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.flash_self_check) refers to function.o(i.flash_test_write_read) for flash_test_write_read
    function.o(i.flash_self_check) refers to function.o(.data) for flash_id
    function.o(i.flash_test_write_read) refers to spi_flash.o(i.spi_flash_sector_erase) for spi_flash_sector_erase
    function.o(i.flash_test_write_read) refers to spi_flash.o(i.spi_flash_buffer_write) for spi_flash_buffer_write
    function.o(i.flash_test_write_read) refers to spi_flash.o(i.spi_flash_buffer_read) for spi_flash_buffer_read
    function.o(i.flash_test_write_read) refers to function.o(i.memory_compare) for memory_compare
    function.o(i.flash_test_write_read) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.flash_test_write_read) refers to function.o(.data) for i
    function.o(i.flash_test_write_read) refers to function.o(.bss) for tx_buffer
    function.o(i.generate_hide_filename) refers to function.o(i.get_datetime_string) for get_datetime_string
    function.o(i.generate_hide_filename) refers to printfa.o(i.__0sprintf) for __2sprintf
    function.o(i.generate_log_filename) refers to printfa.o(i.__0sprintf) for __2sprintf
    function.o(i.generate_log_filename) refers to function.o(.data) for config_data
    function.o(i.generate_overlimit_filename) refers to function.o(i.get_datetime_string) for get_datetime_string
    function.o(i.generate_overlimit_filename) refers to printfa.o(i.__0sprintf) for __2sprintf
    function.o(i.generate_sample_filename) refers to function.o(i.get_datetime_string) for get_datetime_string
    function.o(i.generate_sample_filename) refers to printfa.o(i.__0sprintf) for __2sprintf
    function.o(i.get_current_time) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    function.o(i.get_current_time) refers to function.o(.data) for current_time
    function.o(i.get_current_time_string) refers to function.o(i.get_current_time) for get_current_time
    function.o(i.get_current_time_string) refers to printfa.o(i.__0sprintf) for __2sprintf
    function.o(i.get_current_time_string) refers to function.o(.data) for current_time
    function.o(i.get_datetime_string) refers to function.o(i.get_current_time) for get_current_time
    function.o(i.get_datetime_string) refers to printfa.o(i.__0sprintf) for __2sprintf
    function.o(i.get_datetime_string) refers to function.o(.data) for current_time
    function.o(i.get_unix_timestamp) refers to function.o(i.get_current_time) for get_current_time
    function.o(i.get_unix_timestamp) refers to function.o(.data) for current_time
    function.o(i.hide_command) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.hide_command) refers to function.o(i.write_log_data) for write_log_data
    function.o(i.hide_command) refers to function.o(.data) for sampling_active
    function.o(i.increment_boot_count) refers to function.o(i.save_config_to_flash) for save_config_to_flash
    function.o(i.increment_boot_count) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.increment_boot_count) refers to function.o(.data) for config_data
    function.o(i.init_data_storage) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.init_data_storage) refers to memseta.o(.text) for __aeabi_memclr4
    function.o(i.init_data_storage) refers to function.o(i.create_directories) for create_directories
    function.o(i.init_data_storage) refers to function.o(i.generate_log_filename) for generate_log_filename
    function.o(i.init_data_storage) refers to ff.o(i.f_open) for f_open
    function.o(i.init_data_storage) refers to function.o(i.write_log_data) for write_log_data
    function.o(i.init_data_storage) refers to printfa.o(i.__0sprintf) for __2sprintf
    function.o(i.init_data_storage) refers to function.o(.bss) for file_manager
    function.o(i.init_data_storage) refers to function.o(.data) for config_data
    function.o(i.load_config_from_flash) refers to spi_flash.o(i.spi_flash_buffer_read) for spi_flash_buffer_read
    function.o(i.load_config_from_flash) refers to f2d.o(.text) for __aeabi_f2d
    function.o(i.load_config_from_flash) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    function.o(i.load_config_from_flash) refers to cdcmple.o(.text) for __aeabi_cdcmple
    function.o(i.load_config_from_flash) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.load_config_from_flash) refers to function.o(.data) for config_data
    function.o(i.local_rtc_init) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.local_rtc_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    function.o(i.local_rtc_init) refers to gd32f4xx_pmu.o(i.pmu_backup_write_enable) for pmu_backup_write_enable
    function.o(i.local_rtc_init) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    function.o(i.local_rtc_init) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    function.o(i.local_rtc_init) refers to gd32f4xx_rcu.o(i.rcu_rtc_clock_config) for rcu_rtc_clock_config
    function.o(i.local_rtc_init) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    function.o(i.local_rtc_init) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    function.o(i.nvic_config) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    function.o(i.nvic_config) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    function.o(i.parse_time_input) refers to _scanf_int.o(.text) for _scanf_int
    function.o(i.parse_time_input) refers to __0sscanf.o(.text) for __0sscanf
    function.o(i.parse_time_input) refers to strlen.o(.text) for strlen
    function.o(i.parse_time_input) refers to strncpy.o(.text) for strncpy
    function.o(i.parse_time_input) refers to atoi.o(.text) for atoi
    function.o(i.process_command) refers to strcmp.o(.text) for strcmp
    function.o(i.process_command) refers to function.o(i.system_self_check) for system_self_check
    function.o(i.process_command) refers to function.o(i.rtc_config_command) for rtc_config_command
    function.o(i.process_command) refers to function.o(i.rtc_now_command) for rtc_now_command
    function.o(i.process_command) refers to function.o(i.read_config_from_tf) for read_config_from_tf
    function.o(i.process_command) refers to function.o(i.set_ratio_value) for set_ratio_value
    function.o(i.process_command) refers to function.o(i.set_limit_value) for set_limit_value
    function.o(i.process_command) refers to function.o(i.config_save_command) for config_save_command
    function.o(i.process_command) refers to function.o(i.config_read_command) for config_read_command
    function.o(i.process_command) refers to function.o(i.start_sampling) for start_sampling
    function.o(i.process_command) refers to function.o(i.stop_sampling) for stop_sampling
    function.o(i.process_command) refers to function.o(i.hide_command) for hide_command
    function.o(i.process_command) refers to function.o(i.unhide_command) for unhide_command
    function.o(i.process_command) refers to function.o(i.test_sampling_command) for test_sampling_command
    function.o(i.process_command) refers to function.o(i.test_tf_card_command) for test_tf_card_command
    function.o(i.process_command) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.process_command) refers to function.o(.conststring) for .conststring
    function.o(i.read_adc_voltage) refers to adc.o(i.ADC_Read_Voltage) for ADC_Read_Voltage
    function.o(i.read_config_from_tf) refers to ff.o(i.f_open) for f_open
    function.o(i.read_config_from_tf) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.read_config_from_tf) refers to ff.o(i.f_read) for f_read
    function.o(i.read_config_from_tf) refers to ff.o(i.f_close) for f_close
    function.o(i.read_config_from_tf) refers to strstr.o(.text) for strstr
    function.o(i.read_config_from_tf) refers to strchr.o(.text) for strchr
    function.o(i.read_config_from_tf) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    function.o(i.read_config_from_tf) refers to d2f.o(.text) for __aeabi_d2f
    function.o(i.read_config_from_tf) refers to f2d.o(.text) for __aeabi_f2d
    function.o(i.read_config_from_tf) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    function.o(i.read_config_from_tf) refers to cdcmple.o(.text) for __aeabi_cdcmple
    function.o(i.read_config_from_tf) refers to function.o(i.save_config_to_flash) for save_config_to_flash
    function.o(i.read_config_from_tf) refers to function.o(.bss) for fdst
    function.o(i.read_config_from_tf) refers to function.o(.data) for config_data
    function.o(i.rtc_config_command) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.rtc_config_command) refers to gd32f4xx_pmu.o(i.pmu_backup_write_enable) for pmu_backup_write_enable
    function.o(i.rtc_config_command) refers to gd32f4xx_rcu.o(i.rcu_bkp_reset_enable) for rcu_bkp_reset_enable
    function.o(i.rtc_config_command) refers to gd32f4xx_rcu.o(i.rcu_bkp_reset_disable) for rcu_bkp_reset_disable
    function.o(i.rtc_config_command) refers to function.o(i.local_rtc_init) for local_rtc_init
    function.o(i.rtc_config_command) refers to usart.o(i.usart_clear_buffer) for usart_clear_buffer
    function.o(i.rtc_config_command) refers to usart.o(i.usart_is_command_ready) for usart_is_command_ready
    function.o(i.rtc_config_command) refers to usart.o(i.usart_get_command) for usart_get_command
    function.o(i.rtc_config_command) refers to function.o(i.parse_time_input) for parse_time_input
    function.o(i.rtc_config_command) refers to function.o(i.convert_to_bcd) for convert_to_bcd
    function.o(i.rtc_config_command) refers to gd32f4xx_rtc.o(i.rtc_init) for rtc_init
    function.o(i.rtc_now_command) refers to function.o(i.get_current_time) for get_current_time
    function.o(i.rtc_now_command) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.rtc_now_command) refers to function.o(.data) for current_time
    function.o(i.rtc_reset_command) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.rtc_reset_command) refers to gd32f4xx_pmu.o(i.pmu_backup_write_enable) for pmu_backup_write_enable
    function.o(i.rtc_reset_command) refers to gd32f4xx_rcu.o(i.rcu_bkp_reset_enable) for rcu_bkp_reset_enable
    function.o(i.rtc_reset_command) refers to gd32f4xx_rcu.o(i.rcu_bkp_reset_disable) for rcu_bkp_reset_disable
    function.o(i.rtc_status_check) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.rtc_status_check) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    function.o(i.save_config_to_flash) refers to spi_flash.o(i.spi_flash_sector_erase) for spi_flash_sector_erase
    function.o(i.save_config_to_flash) refers to spi_flash.o(i.spi_flash_buffer_write) for spi_flash_buffer_write
    function.o(i.save_config_to_flash) refers to function.o(.data) for config_data
    function.o(i.set_limit_value) refers to f2d.o(.text) for __aeabi_f2d
    function.o(i.set_limit_value) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.set_limit_value) refers to usart.o(i.usart_clear_buffer) for usart_clear_buffer
    function.o(i.set_limit_value) refers to usart.o(i.usart_is_command_ready) for usart_is_command_ready
    function.o(i.set_limit_value) refers to usart.o(i.usart_get_command) for usart_get_command
    function.o(i.set_limit_value) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    function.o(i.set_limit_value) refers to d2f.o(.text) for __aeabi_d2f
    function.o(i.set_limit_value) refers to strlen.o(.text) for strlen
    function.o(i.set_limit_value) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    function.o(i.set_limit_value) refers to cdcmple.o(.text) for __aeabi_cdcmple
    function.o(i.set_limit_value) refers to function.o(i.save_config_to_flash) for save_config_to_flash
    function.o(i.set_limit_value) refers to function.o(.data) for config_data
    function.o(i.set_ratio_value) refers to f2d.o(.text) for __aeabi_f2d
    function.o(i.set_ratio_value) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.set_ratio_value) refers to usart.o(i.usart_clear_buffer) for usart_clear_buffer
    function.o(i.set_ratio_value) refers to usart.o(i.usart_is_command_ready) for usart_is_command_ready
    function.o(i.set_ratio_value) refers to usart.o(i.usart_get_command) for usart_get_command
    function.o(i.set_ratio_value) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    function.o(i.set_ratio_value) refers to d2f.o(.text) for __aeabi_d2f
    function.o(i.set_ratio_value) refers to strlen.o(.text) for strlen
    function.o(i.set_ratio_value) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    function.o(i.set_ratio_value) refers to cdcmple.o(.text) for __aeabi_cdcmple
    function.o(i.set_ratio_value) refers to function.o(i.save_config_to_flash) for save_config_to_flash
    function.o(i.set_ratio_value) refers to function.o(.data) for config_data
    function.o(i.start_sampling) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.start_sampling) refers to function.o(i.write_log_data) for write_log_data
    function.o(i.start_sampling) refers to function.o(i.update_oled_display) for update_oled_display
    function.o(i.start_sampling) refers to function.o(.data) for sampling_active
    function.o(i.stop_sampling) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    function.o(i.stop_sampling) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.stop_sampling) refers to function.o(i.write_log_data) for write_log_data
    function.o(i.stop_sampling) refers to function.o(i.update_oled_display) for update_oled_display
    function.o(i.stop_sampling) refers to function.o(.data) for sampling_active
    function.o(i.system_self_check) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.system_self_check) refers to spi_flash.o(i.spi_flash_read_id) for spi_flash_read_id
    function.o(i.system_self_check) refers to diskio.o(i.disk_initialize) for disk_initialize
    function.o(i.system_self_check) refers to ff.o(i.f_getfree) for f_getfree
    function.o(i.system_self_check) refers to function.o(i.get_current_time) for get_current_time
    function.o(i.system_self_check) refers to function.o(.data) for flash_id
    function.o(i.test_parameter_protection) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.test_parameter_protection) refers to f2d.o(.text) for __aeabi_f2d
    function.o(i.test_parameter_protection) refers to function.o(.data) for config_data
    function.o(i.test_sampling_command) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.test_sampling_command) refers to function.o(i.get_current_time) for get_current_time
    function.o(i.test_sampling_command) refers to printfa.o(i.__0sprintf) for __2sprintf
    function.o(i.test_sampling_command) refers to function.o(i.get_unix_timestamp) for get_unix_timestamp
    function.o(i.test_sampling_command) refers to function.o(i.encode_voltage_hex) for encode_voltage_hex
    function.o(i.test_sampling_command) refers to f2d.o(.text) for __aeabi_f2d
    function.o(i.test_sampling_command) refers to function.o(i.write_hide_data) for write_hide_data
    function.o(i.test_sampling_command) refers to function.o(i.write_sample_data) for write_sample_data
    function.o(i.test_sampling_command) refers to function.o(i.write_overlimit_data) for write_overlimit_data
    function.o(i.test_sampling_command) refers to function.o(.bss) for file_manager
    function.o(i.test_sampling_command) refers to function.o(.data) for hide_mode
    function.o(i.test_tf_card_command) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.test_tf_card_command) refers to diskio.o(i.disk_initialize) for disk_initialize
    function.o(i.test_tf_card_command) refers to ff.o(i.f_getfree) for f_getfree
    function.o(i.test_tf_card_command) refers to ff.o(i.f_open) for f_open
    function.o(i.test_tf_card_command) refers to memcpya.o(.text) for __aeabi_memcpy4
    function.o(i.test_tf_card_command) refers to strlen.o(.text) for strlen
    function.o(i.test_tf_card_command) refers to ff.o(i.f_write) for f_write
    function.o(i.test_tf_card_command) refers to ff.o(i.f_close) for f_close
    function.o(i.test_tf_card_command) refers to ff.o(i.f_read) for f_read
    function.o(i.test_tf_card_command) refers to strcmp.o(.text) for strcmp
    function.o(i.test_tf_card_command) refers to ff.o(i.f_unlink) for f_unlink
    function.o(i.toggle_sampling_by_key) refers to function.o(i.start_sampling) for start_sampling
    function.o(i.toggle_sampling_by_key) refers to function.o(i.stop_sampling) for stop_sampling
    function.o(i.toggle_sampling_by_key) refers to function.o(.data) for sampling_active
    function.o(i.unhide_command) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.unhide_command) refers to function.o(i.write_log_data) for write_log_data
    function.o(i.unhide_command) refers to function.o(.data) for sampling_active
    function.o(i.update_oled_display) refers to function.o(i.get_current_time) for get_current_time
    function.o(i.update_oled_display) refers to printfa.o(i.__0sprintf) for __2sprintf
    function.o(i.update_oled_display) refers to function.o(i.read_adc_voltage) for read_adc_voltage
    function.o(i.update_oled_display) refers to f2d.o(.text) for __aeabi_f2d
    function.o(i.update_oled_display) refers to oled.o(i.OLED_Clear) for OLED_Clear
    function.o(i.update_oled_display) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    function.o(i.update_oled_display) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    function.o(i.update_oled_display) refers to function.o(.data) for sampling_active
    function.o(i.update_sampling) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    function.o(i.update_sampling) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    function.o(i.update_sampling) refers to function.o(i.get_current_time) for get_current_time
    function.o(i.update_sampling) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.update_sampling) refers to function.o(i.read_adc_voltage) for read_adc_voltage
    function.o(i.update_sampling) refers to function.o(i.get_current_time_string) for get_current_time_string
    function.o(i.update_sampling) refers to function.o(i.get_unix_timestamp) for get_unix_timestamp
    function.o(i.update_sampling) refers to function.o(i.encode_voltage_hex) for encode_voltage_hex
    function.o(i.update_sampling) refers to f2d.o(.text) for __aeabi_f2d
    function.o(i.update_sampling) refers to function.o(i.write_hide_data) for write_hide_data
    function.o(i.update_sampling) refers to printfa.o(i.__0sprintf) for __2sprintf
    function.o(i.update_sampling) refers to function.o(i.write_sample_data) for write_sample_data
    function.o(i.update_sampling) refers to function.o(i.write_overlimit_data) for write_overlimit_data
    function.o(i.update_sampling) refers to function.o(i.update_oled_display) for update_oled_display
    function.o(i.update_sampling) refers to function.o(.data) for sampling_active
    function.o(i.update_sampling) refers to function.o(.bss) for file_manager
    function.o(i.write_file) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.write_file) refers to usart.o(i.usart_clear_buffer) for usart_clear_buffer
    function.o(i.write_file) refers to usart.o(i.usart_is_command_ready) for usart_is_command_ready
    function.o(i.write_file) refers to usart.o(i.usart_get_command) for usart_get_command
    function.o(i.write_file) refers to strncpy.o(.text) for strncpy
    function.o(i.write_file) refers to function.o(.bss) for filebuffer
    function.o(i.write_hide_data) refers to f2d.o(.text) for __aeabi_f2d
    function.o(i.write_hide_data) refers to printfa.o(i.__0sprintf) for __2sprintf
    function.o(i.write_hide_data) refers to function.o(i.get_unix_timestamp) for get_unix_timestamp
    function.o(i.write_hide_data) refers to function.o(i.encode_voltage_hex) for encode_voltage_hex
    function.o(i.write_hide_data) refers to ff.o(i.f_mkdir) for f_mkdir
    function.o(i.write_hide_data) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.write_hide_data) refers to function.o(i.generate_hide_filename) for generate_hide_filename
    function.o(i.write_hide_data) refers to ff.o(i.f_open) for f_open
    function.o(i.write_hide_data) refers to strlen.o(.text) for strlen
    function.o(i.write_hide_data) refers to ff.o(i.f_write) for f_write
    function.o(i.write_hide_data) refers to ff.o(i.f_sync) for f_sync
    function.o(i.write_hide_data) refers to function.o(i.check_and_create_new_file) for check_and_create_new_file
    function.o(i.write_hide_data) refers to function.o(.bss) for file_manager
    function.o(i.write_log_data) refers to function.o(i.get_current_time_string) for get_current_time_string
    function.o(i.write_log_data) refers to printfa.o(i.__0sprintf) for __2sprintf
    function.o(i.write_log_data) refers to strlen.o(.text) for strlen
    function.o(i.write_log_data) refers to ff.o(i.f_write) for f_write
    function.o(i.write_log_data) refers to ff.o(i.f_sync) for f_sync
    function.o(i.write_log_data) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.write_log_data) refers to function.o(.bss) for file_manager
    function.o(i.write_overlimit_data) refers to f2d.o(.text) for __aeabi_f2d
    function.o(i.write_overlimit_data) refers to printfa.o(i.__0sprintf) for __2sprintf
    function.o(i.write_overlimit_data) refers to ff.o(i.f_mkdir) for f_mkdir
    function.o(i.write_overlimit_data) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.write_overlimit_data) refers to function.o(i.generate_overlimit_filename) for generate_overlimit_filename
    function.o(i.write_overlimit_data) refers to ff.o(i.f_open) for f_open
    function.o(i.write_overlimit_data) refers to strlen.o(.text) for strlen
    function.o(i.write_overlimit_data) refers to ff.o(i.f_write) for f_write
    function.o(i.write_overlimit_data) refers to ff.o(i.f_sync) for f_sync
    function.o(i.write_overlimit_data) refers to function.o(i.check_and_create_new_file) for check_and_create_new_file
    function.o(i.write_overlimit_data) refers to function.o(.bss) for file_manager
    function.o(i.write_sample_data) refers to ff.o(i.f_mkdir) for f_mkdir
    function.o(i.write_sample_data) refers to printfa.o(i.__0printf) for __2printf
    function.o(i.write_sample_data) refers to function.o(i.generate_sample_filename) for generate_sample_filename
    function.o(i.write_sample_data) refers to ff.o(i.f_open) for f_open
    function.o(i.write_sample_data) refers to strlen.o(.text) for strlen
    function.o(i.write_sample_data) refers to ff.o(i.f_write) for f_write
    function.o(i.write_sample_data) refers to ff.o(i.f_sync) for f_sync
    function.o(i.write_sample_data) refers to function.o(i.check_and_create_new_file) for check_and_create_new_file
    function.o(i.write_sample_data) refers to function.o(.bss) for file_manager
    system_gd32f4xx.o(i.SystemCoreClockUpdate) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    system_gd32f4xx.o(i.SystemInit) refers to system_gd32f4xx.o(i.system_clock_config) for system_clock_config
    system_gd32f4xx.o(i.system_clock_config) refers to system_gd32f4xx.o(i.system_clock_240m_25m_hxtal) for system_clock_240m_25m_hxtal
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_debug_freeze_disable) refers to gd32f4xx_dbg.o(i.dbg_periph_disable) for dbg_periph_disable
    gd32f4xx_can.o(i.can_debug_freeze_enable) refers to gd32f4xx_dbg.o(i.dbg_periph_enable) for dbg_periph_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_receive_message_length_get) for can_receive_message_length_get
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_error_get) for can_error_get
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_enet.o(i.enet_initpara_reset) for enet_initpara_reset
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_tx_disable) for enet_tx_disable
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_rx_disable) for enet_rx_disable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_tx_enable) for enet_tx_enable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_rx_enable) for enet_rx_enable
    gd32f4xx_enet.o(i.enet_frame_receive) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_frame_transmit) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_config) for enet_phy_config
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_default_init) for enet_default_init
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_config) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_reset) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_phyloopback_disable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phyloopback_enable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_registers_get) refers to gd32f4xx_enet.o(.constdata) for enet_reg_tab
    gd32f4xx_enet.o(i.enet_rxframe_drop) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(i.enet_rxframe_drop) for enet_rxframe_drop
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxprocess_check_recovery) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_tx_disable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_enet.o(i.enet_tx_enable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_fmc.o(i.fmc_bank0_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_bank1_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_byte_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_halfword_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_mass_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_page_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_ready_wait) refers to gd32f4xx_fmc.o(i.fmc_state_get) for fmc_state_get
    gd32f4xx_fmc.o(i.fmc_sector_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_word_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_security_protection_config) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_user_write) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_i2c.o(i.i2c_clock_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_misc.o(i.nvic_irq_enable) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_pmu.o(i.pmu_highdriver_switch_select) refers to gd32f4xx_pmu.o(i.pmu_flag_get) for pmu_flag_get
    gd32f4xx_pmu.o(i.pmu_to_deepsleepmode) refers to gd32f4xx_pmu.o(.bss) for reg_snap
    gd32f4xx_rcu.o(i.rcu_deinit) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_rcu.o(i.rcu_osci_stab_wait) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_second_adjust) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_i2s_clock_config) for rcu_i2s_clock_config
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_external_clock_mode0_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_clock_mode1_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_timer.o(i.timer_input_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_input_pwm_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_usart.o(i.usart_baudrate_set) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(.text) for Reset_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.USART0_IRQHandler) for USART0_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SDIO_IRQHandler) for SDIO_IRQHandler
    startup_gd32f450_470.o(.text) refers to system_gd32f4xx.o(i.SystemInit) for SystemInit
    startup_gd32f450_470.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    diskio.o(i.disk_initialize) refers to sdcard.o(i.sd_init) for sd_init
    diskio.o(i.disk_initialize) refers to sdcard.o(i.sd_card_information_get) for sd_card_information_get
    diskio.o(i.disk_initialize) refers to sdcard.o(i.sd_card_select_deselect) for sd_card_select_deselect
    diskio.o(i.disk_initialize) refers to sdcard.o(i.sd_cardstatus_get) for sd_cardstatus_get
    diskio.o(i.disk_initialize) refers to sdcard.o(i.sd_bus_mode_config) for sd_bus_mode_config
    diskio.o(i.disk_initialize) refers to sdcard.o(i.sd_transfer_mode_config) for sd_transfer_mode_config
    diskio.o(i.disk_read) refers to sdcard.o(i.sd_block_read) for sd_block_read
    diskio.o(i.disk_read) refers to sdcard.o(i.sd_multiblocks_read) for sd_multiblocks_read
    diskio.o(i.disk_write) refers to sdcard.o(i.sd_block_write) for sd_block_write
    diskio.o(i.disk_write) refers to sdcard.o(i.sd_multiblocks_write) for sd_multiblocks_write
    ff.o(i.check_fs) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.chk_mounted) refers to diskio.o(i.disk_status) for disk_status
    ff.o(i.chk_mounted) refers to diskio.o(i.disk_initialize) for disk_initialize
    ff.o(i.chk_mounted) refers to ff.o(i.check_fs) for check_fs
    ff.o(i.chk_mounted) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.chk_mounted) refers to ff.o(.data) for FatFs
    ff.o(i.create_chain) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.create_chain) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.create_name) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.create_name) refers to ff.o(i.chk_chr) for chk_chr
    ff.o(i.dir_find) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_find) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_find) refers to ff.o(i.mem_cmp) for mem_cmp
    ff.o(i.dir_find) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_next) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.dir_next) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.dir_next) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_next) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.dir_next) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.dir_read) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_read) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_register) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_register) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_register) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_register) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.dir_register) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.dir_remove) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_remove) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_sdi) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.dir_sdi) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_chmod) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_chmod) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_chmod) refers to ff.o(i.sync) for sync
    ff.o(i.f_close) refers to ff.o(i.f_sync) for f_sync
    ff.o(i.f_getfree) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_getfree) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_getfree) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_gets) refers to ff.o(i.f_read) for f_read
    ff.o(i.f_lseek) refers to ff.o(i.validate) for validate
    ff.o(i.f_lseek) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_lseek) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_lseek) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_lseek) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_lseek) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.f_mkdir) refers to diskio.o(i.get_fattime) for get_fattime
    ff.o(i.f_mkdir) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_mkdir) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_mkdir) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_mkdir) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_mkdir) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_mkdir) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.f_mkdir) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_mkdir) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_mkdir) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_mkdir) refers to ff.o(i.sync) for sync
    ff.o(i.f_mount) refers to ff.o(.data) for FatFs
    ff.o(i.f_open) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_open) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_open) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_open) refers to diskio.o(i.get_fattime) for get_fattime
    ff.o(i.f_open) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_open) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_opendir) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_opendir) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_opendir) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_printf) refers to ff.o(i.f_putc) for f_putc
    ff.o(i.f_printf) refers to ff.o(i.f_puts) for f_puts
    ff.o(i.f_putc) refers to ff.o(i.f_write) for f_write
    ff.o(i.f_puts) refers to ff.o(i.f_putc) for f_putc
    ff.o(i.f_read) refers to ff.o(i.validate) for validate
    ff.o(i.f_read) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_read) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_read) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.f_read) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_read) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_readdir) refers to ff.o(i.validate) for validate
    ff.o(i.f_readdir) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_readdir) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.f_readdir) refers to ff.o(i.get_fileinfo) for get_fileinfo
    ff.o(i.f_readdir) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.f_rename) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_rename) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_rename) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_rename) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_rename) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_rename) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_rename) refers to ff.o(i.dir_remove) for dir_remove
    ff.o(i.f_rename) refers to ff.o(i.sync) for sync
    ff.o(i.f_stat) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_stat) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_stat) refers to ff.o(i.get_fileinfo) for get_fileinfo
    ff.o(i.f_sync) refers to ff.o(i.validate) for validate
    ff.o(i.f_sync) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_sync) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_sync) refers to diskio.o(i.get_fattime) for get_fattime
    ff.o(i.f_sync) refers to ff.o(i.sync) for sync
    ff.o(i.f_truncate) refers to ff.o(i.validate) for validate
    ff.o(i.f_truncate) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_truncate) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_truncate) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.f_unlink) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_unlink) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_unlink) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_unlink) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_unlink) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.f_unlink) refers to ff.o(i.dir_remove) for dir_remove
    ff.o(i.f_unlink) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_unlink) refers to ff.o(i.sync) for sync
    ff.o(i.f_utime) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_utime) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_utime) refers to ff.o(i.sync) for sync
    ff.o(i.f_write) refers to ff.o(i.validate) for validate
    ff.o(i.f_write) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_write) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_write) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_write) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_write) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.follow_path) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.follow_path) refers to ff.o(i.create_name) for create_name
    ff.o(i.follow_path) refers to ff.o(i.dir_find) for dir_find
    ff.o(i.get_fat) refers to ff.o(i.move_window) for move_window
    ff.o(i.move_window) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.move_window) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.put_fat) refers to ff.o(i.move_window) for move_window
    ff.o(i.remove_chain) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.remove_chain) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.sync) refers to ff.o(i.move_window) for move_window
    ff.o(i.sync) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.sync) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.sync) refers to diskio.o(i.disk_ioctl) for disk_ioctl
    ff.o(i.validate) refers to diskio.o(i.disk_status) for disk_status
    atof.o(i.__hardfp_atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.__hardfp_atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.__hardfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.__hardfp_atof) refers to errno.o(i.__set_errno) for __set_errno
    atof.o(i.__softfp_atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.__softfp_atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.__softfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.__softfp_atof) refers to errno.o(i.__set_errno) for __set_errno
    atof.o(i.atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.atof) refers to errno.o(i.__set_errno) for __set_errno
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    _scanf_int.o(.text) refers to _chval.o(.text) for _chval
    atoi.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace_o.o(.text) for isspace
    strtod.o(.text) refers to scanf_fp.o(.text) for _scanf_real
    strtod.o(.text) refers to _sgetc.o(.text) for _sgetc
    strtod.o(.text) refers to isspace_o.o(.text) for isspace
    strtol.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    isspace_c.o(.text) refers to ctype_c.o(.text) for __ctype_lookup
    ctype_o.o(.text) refers to ctype_o.o(.constdata) for .constdata
    ctype_o.o(.constdata) refers to ctype_o.o(.constdata) for __ctype_table
    isalnum_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isalpha_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isblank_o.o(.text) refers to ctype_o.o(.constdata) for __ctype_table
    iscntrl_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isgraph_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    islower_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isprint_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    ispunct_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isspace_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isupper_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isxdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    _scanf.o(.text) refers (Weak) to scanf_fp.o(.text) for _scanf_real
    _scanf.o(.text) refers (Weak) to _scanf_int.o(.text) for _scanf_int
    scanf_fp.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    scanf_fp.o(.text) refers to dfltul.o(.text) for __aeabi_ul2d
    scanf_fp.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    scanf_fp.o(.text) refers to ddiv.o(.text) for __aeabi_ddiv
    scanf_fp.o(.text) refers to scanf_fp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    ctype_c.o(.text) refers to ctype_c.o(.constdata) for .constdata
    dfltul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    scanf_fp.o(i._is_digit) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing gd32f4xx_it.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_it.o(.revsh_text), (4 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing systick.o(.rev16_text), (4 bytes).
    Removing systick.o(.revsh_text), (4 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing sdcard.o(.rev16_text), (4 bytes).
    Removing sdcard.o(.revsh_text), (4 bytes).
    Removing sdcard.o(i.sd_card_capacity_get), (168 bytes).
    Removing sdcard.o(i.sd_erase), (324 bytes).
    Removing sdcard.o(i.sd_lock_unlock), (488 bytes).
    Removing sdcard.o(i.sd_power_off), (14 bytes).
    Removing sdcard.o(i.sd_sdstatus_get), (384 bytes).
    Removing sdcard.o(i.sd_transfer_state_get), (20 bytes).
    Removing spi_flash.o(.rev16_text), (4 bytes).
    Removing spi_flash.o(.revsh_text), (4 bytes).
    Removing spi_flash.o(i.spi_flash_bulk_erase), (44 bytes).
    Removing spi_flash.o(i.spi_flash_read_byte), (10 bytes).
    Removing spi_flash.o(i.spi_flash_send_halfword), (52 bytes).
    Removing spi_flash.o(i.spi_flash_start_read_sequence), (48 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(i.KEY_Stat), (40 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(i.OLED_ColorTurn), (28 bytes).
    Removing oled.o(i.OLED_DisPlay_Off), (28 bytes).
    Removing oled.o(i.OLED_DisPlay_On), (28 bytes).
    Removing oled.o(i.OLED_DisplayTurn), (44 bytes).
    Removing oled.o(i.OLED_DrawCircle), (152 bytes).
    Removing oled.o(i.OLED_DrawLine), (146 bytes).
    Removing oled.o(i.OLED_Pow), (22 bytes).
    Removing oled.o(i.OLED_ScrollDisplay), (164 bytes).
    Removing oled.o(i.OLED_ShowChinese), (264 bytes).
    Removing oled.o(i.OLED_ShowNum), (116 bytes).
    Removing oled.o(i.OLED_ShowPicture), (78 bytes).
    Removing oled.o(i.OLED_WR_BP), (46 bytes).
    Removing oled.o(.data), (5688 bytes).
    Removing function.o(.rev16_text), (4 bytes).
    Removing function.o(.revsh_text), (4 bytes).
    Removing function.o(i.check_command_match), (408 bytes).
    Removing function.o(i.flash_self_check), (140 bytes).
    Removing function.o(i.flash_test_write_read), (200 bytes).
    Removing function.o(i.memory_compare), (36 bytes).
    Removing function.o(i.process_command), (400 bytes).
    Removing function.o(i.rtc_reset_command), (292 bytes).
    Removing function.o(i.rtc_status_check), (672 bytes).
    Removing function.o(i.test_parameter_protection), (660 bytes).
    Removing function.o(i.test_sampling_command), (724 bytes).
    Removing function.o(i.test_tf_card_command), (936 bytes).
    Removing function.o(i.write_file), (88 bytes).
    Removing system_gd32f4xx.o(.rev16_text), (4 bytes).
    Removing system_gd32f4xx.o(.revsh_text), (4 bytes).
    Removing system_gd32f4xx.o(i.SystemCoreClockUpdate), (272 bytes).
    Removing gd32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_adc.o(i.adc_channel_16_to_18), (96 bytes).
    Removing gd32f4xx_adc.o(i.adc_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_discontinuous_mode_config), (82 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_mode_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_mode_enable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_request_after_last_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_request_after_last_enable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_end_of_conversion_config), (34 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_config), (124 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_offset_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_data_read), (46 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_software_startconv_flag_get), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_disable), (66 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_enable), (66 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_clear), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_get), (112 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_config), (58 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_disable), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_enable), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_resolution_config), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_routine_software_startconv_flag_get), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_delay_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_disable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_mode_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_routine_data_read), (12 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_disable), (50 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_sequence_channel_enable), (64 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_threshold_config), (14 bytes).
    Removing gd32f4xx_can.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_can.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_can.o(i.can1_filter_start_bank), (56 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_disable), (44 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_enable), (44 bytes).
    Removing gd32f4xx_can.o(i.can_deinit), (52 bytes).
    Removing gd32f4xx_can.o(i.can_error_get), (12 bytes).
    Removing gd32f4xx_can.o(i.can_fifo_release), (32 bytes).
    Removing gd32f4xx_can.o(i.can_filter_init), (272 bytes).
    Removing gd32f4xx_can.o(i.can_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_flag_get), (30 bytes).
    Removing gd32f4xx_can.o(i.can_init), (290 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_disable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_enable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_get), (116 bytes).
    Removing gd32f4xx_can.o(i.can_message_receive), (228 bytes).
    Removing gd32f4xx_can.o(i.can_message_transmit), (336 bytes).
    Removing gd32f4xx_can.o(i.can_receive_error_number_get), (8 bytes).
    Removing gd32f4xx_can.o(i.can_receive_message_length_get), (26 bytes).
    Removing gd32f4xx_can.o(i.can_struct_para_init), (164 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_disable), (48 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_enable), (48 bytes).
    Removing gd32f4xx_can.o(i.can_transmission_stop), (80 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_error_number_get), (10 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_states), (124 bytes).
    Removing gd32f4xx_can.o(i.can_wakeup), (48 bytes).
    Removing gd32f4xx_can.o(i.can_working_mode_set), (168 bytes).
    Removing gd32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_crc.o(i.crc_block_data_calculate), (36 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_reset), (20 bytes).
    Removing gd32f4xx_crc.o(i.crc_deinit), (24 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_write), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_single_data_calculate), (16 bytes).
    Removing gd32f4xx_ctc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ctc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_clock_limit_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_capture_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_direction_read), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_disable), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_enable), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_deinit), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_clear), (36 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_get), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_hardware_trim_mode_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_get), (56 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_polarity_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_prescaler_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_signal_select), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_software_refsource_pulse_generate), (20 bytes).
    Removing gd32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_data_set), (48 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_disable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_enable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_disable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_enable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_software_trigger_enable), (12 bytes).
    Removing gd32f4xx_dac.o(i.dac_data_set), (64 bytes).
    Removing gd32f4xx_dac.o(i.dac_deinit), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_dma_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_dma_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_clear), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_get), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_disable), (18 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_enable), (18 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_clear), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_get), (38 bytes).
    Removing gd32f4xx_dac.o(i.dac_lfsr_noise_config), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_value_get), (22 bytes).
    Removing gd32f4xx_dac.o(i.dac_software_trigger_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_triangle_noise_config), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_source_config), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_wave_mode_config), (40 bytes).
    Removing gd32f4xx_dbg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dbg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_deinit), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_id_get), (12 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_disable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_enable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_disable), (32 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_enable), (32 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_disable), (20 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_enable), (20 bytes).
    Removing gd32f4xx_dci.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dci.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_data_read), (12 bytes).
    Removing gd32f4xx_dci.o(i.dci_deinit), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_flag_get), (36 bytes).
    Removing gd32f4xx_dci.o(i.dci_init), (52 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_disable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_enable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_unmask_config), (24 bytes).
    Removing gd32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dma.o(i.dma_circulation_disable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_circulation_enable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_fifo_status_get), (20 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_disable), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_enable), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_flag_clear), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_flag_get), (516 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_address_config), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_address_generation_config), (64 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_burst_beats_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_width_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_multi_data_para_struct_init), (40 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_address_config), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_burst_beats_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_width_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_peripheral_address_generation_config), (126 bytes).
    Removing gd32f4xx_dma.o(i.dma_priority_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_single_data_mode_init), (344 bytes).
    Removing gd32f4xx_dma.o(i.dma_single_data_para_struct_init), (34 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_config), (76 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_enable), (66 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_direction_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_number_config), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_number_get), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_using_memory_get), (28 bytes).
    Removing gd32f4xx_enet.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_enet.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_config), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_disable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_enable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_current_desc_address_get), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_debug_status_get), (108 bytes).
    Removing gd32f4xx_enet.o(i.enet_default_init), (148 bytes).
    Removing gd32f4xx_enet.o(i.enet_deinit), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_delay), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_clear), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_get), (14 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_set), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_information_get), (100 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_select_normal_mode), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_chain_init), (200 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_ring_init), (236 bytes).
    Removing gd32f4xx_enet.o(i.enet_disable), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_resume), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_state_get), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_enable), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_get), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_enable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_threshold_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_disable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_enable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_frame_receive), (248 bytes).
    Removing gd32f4xx_enet.o(i.enet_frame_transmit), (204 bytes).
    Removing gd32f4xx_enet.o(i.enet_init), (868 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_config), (356 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_reset), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_disable), (72 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_enable), (72 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_get), (60 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_set), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_missed_frame_counter_get), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_get), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_preset_config), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_reset), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_disable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_enable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_config), (44 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_detect_config), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_generate), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_phy_config), (216 bytes).
    Removing gd32f4xx_enet.o(i.enet_phy_write_read), (156 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_disable), (50 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_enable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_expected_time_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init), (236 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init), (280 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_pps_output_frequency_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_subsecond_increment_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_system_time_get), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_addend_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_function_config), (256 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_update_config), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode), (340 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode), (444 bytes).
    Removing gd32f4xx_enet.o(i.enet_registers_get), (56 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_delay_receive_complete_interrupt), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_immediate_receive_complete_interrupt), (10 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_disable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_enable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxframe_drop), (172 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxframe_size_get), (152 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxprocess_check_recovery), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_software_reset), (60 bytes).
    Removing gd32f4xx_enet.o(i.enet_transmit_checksum_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_enable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_txfifo_flush), (52 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_config), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_register_pointer_reset), (20 bytes).
    Removing gd32f4xx_enet.o(.bss), (15460 bytes).
    Removing gd32f4xx_enet.o(.constdata), (116 bytes).
    Removing gd32f4xx_enet.o(.data), (20 bytes).
    Removing gd32f4xx_exmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_ecc_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_clear), (52 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_get), (52 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_disable), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_enable), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_clear), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_get), (72 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_deinit), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_disable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_ecc_config), (48 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_enable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_init), (172 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_struct_para_init), (54 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_consecutive_clock_config), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_disable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_enable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_init), (228 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_page_size_config), (40 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_struct_para_init), (106 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_disable), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_enable), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_init), (188 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_struct_para_init), (60 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_autorefresh_number_set), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_bankstatus_get), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_command_config), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_deinit), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_init), (284 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_config), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_enable), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_refresh_count_set), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_struct_command_para_init), (16 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_struct_para_init), (66 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_write_protection_config), (64 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_deinit), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_high_id_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_init), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_low_id_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_command_set), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_id_command_send), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_send_command_state_get), (48 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_struct_para_init), (20 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_cmd_send), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_command_set), (28 bytes).
    Removing gd32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exti.o(i.exti_deinit), (28 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_clear), (12 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_get), (24 bytes).
    Removing gd32f4xx_exti.o(i.exti_init), (188 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank0_erase), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank1_erase), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_byte_program), (80 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_get), (24 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_halfword_program), (84 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_get), (64 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_lock), (20 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_mass_erase), (72 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_page_erase), (124 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_ready_wait), (32 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_sector_erase), (96 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_state_get), (76 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_unlock), (36 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_word_program), (84 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_wscnt_set), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_boot_mode_config), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_double_bank_select), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp0_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp1_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_disable), (96 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_enable), (84 bytes).
    Removing gd32f4xx_fmc.o(i.ob_erase), (76 bytes).
    Removing gd32f4xx_fmc.o(i.ob_lock), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_security_protection_config), (40 bytes).
    Removing gd32f4xx_fmc.o(i.ob_spc_get), (28 bytes).
    Removing gd32f4xx_fmc.o(i.ob_start), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_unlock), (36 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_write), (52 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection0_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection1_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_disable), (72 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_enable), (72 bytes).
    Removing gd32f4xx_fwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_config), (104 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_counter_reload), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_enable), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_flag_get), (24 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_prescaler_value_config), (60 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_reload_value_config), (64 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_disable), (12 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_enable), (16 bytes).
    Removing gd32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_write), (10 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_deinit), (206 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_input_port_get), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_bit_get), (16 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_port_get), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_pin_lock), (18 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_toggle), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_write), (4 bytes).
    Removing gd32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_ack_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_ackpos_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_enable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_clock_config), (228 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_receive), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_transmit), (6 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_deinit), (88 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_digital_noise_filter_config), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dma_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dma_last_transfer_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_enable), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_enable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_flag_clear), (40 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_flag_get), (30 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_disable), (26 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_enable), (26 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_clear), (44 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_get), (92 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_master_addressing), (20 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_mode_addr_config), (28 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_transfer_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_value_get), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_disable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_enable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_disable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_enable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_slave_response_to_gcall_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_alert_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_arp_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_type_config), (24 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_software_reset_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_start_on_bus), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_stop_on_bus), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_stretch_scl_low_config), (16 bytes).
    Removing gd32f4xx_ipa.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ipa.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_init), (164 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_init), (100 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_loading_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_deinit), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_init), (316 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_struct_para_init), (22 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_get), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_init), (164 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_init), (100 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_loading_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_inter_timer_config), (36 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interval_clock_num_config), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_line_mark_config), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_pixel_format_convert_mode_set), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_disable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_disable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_enable), (20 bytes).
    Removing gd32f4xx_iref.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_iref.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_iref.o(i.iref_deinit), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_disable), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_enable), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_mode_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_precision_trim_value_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_sink_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_step_data_config), (28 bytes).
    Removing gd32f4xx_misc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_misc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_misc.o(i.nvic_irq_disable), (24 bytes).
    Removing gd32f4xx_misc.o(i.nvic_vector_table_set), (24 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_reset), (16 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_set), (16 bytes).
    Removing gd32f4xx_misc.o(i.systick_clksource_set), (40 bytes).
    Removing gd32f4xx_pmu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_pmu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_ldo_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_write_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_deinit), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_clear), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_get), (24 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_switch_select), (44 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_ldo_output_select), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowpower_driver_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_select), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_normalpower_driver_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_deepsleepmode), (244 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_sleepmode), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_standbymode), (108 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(.bss), (16 bytes).
    Removing gd32f4xx_rcu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rcu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ahb_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb1_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb2_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ck48m_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout0_config), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout1_config), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deepsleep_voltage_set), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deinit), (140 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_i2s_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_enable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_irc16m_adjust_value_set), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_lxtal_drive_capability_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_disable), (116 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_enable), (116 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_off), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_enable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pll48m_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pll_config), (132 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_plli2s_config), (44 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pllsai_config), (72 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_rtc_div_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_config), (32 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_system_clock_source_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_system_clock_source_get), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_timer_clock_prescaler_config), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_tli_clock_div_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_voltage_key_unlock), (16 bytes).
    Removing gd32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_config), (100 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_disable), (128 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_enable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_get), (68 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_output_config), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_config), (52 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_get), (32 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_disable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_enable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_calibration_output_config), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_config), (116 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_deinit), (204 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_flag_clear), (16 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_flag_get), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_hour_adjust), (36 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_disable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_enable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_disable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_enable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_second_adjust), (108 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_smooth_calibration_config), (80 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_subsecond_get), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper0_pin_map), (28 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_disable), (16 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_enable), (200 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_disable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_enable), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_get), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_pin_map), (28 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_subsecond_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_clock_set), (92 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_disable), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_enable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_set), (76 bytes).
    Removing gd32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_csm_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_fifo_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_hardware_clock_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_type_set), (40 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_enable), (20 bytes).
    Removing gd32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_spi.o(i.i2s_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_full_duplex_mode_config), (48 bytes).
    Removing gd32f4xx_spi.o(i.i2s_init), (28 bytes).
    Removing gd32f4xx_spi.o(i.i2s_psc_config), (292 bytes).
    Removing gd32f4xx_spi.o(i.spi_bidirectional_transfer_config), (26 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_error_clear), (8 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_get), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_next), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_off), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_on), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_get), (8 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_set), (4 bytes).
    Removing gd32f4xx_spi.o(i.spi_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_dma_disable), (22 bytes).
    Removing gd32f4xx_spi.o(i.spi_dma_enable), (22 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_data_frame_format_config), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_deinit), (172 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_format_error_clear), (6 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_disable), (48 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_enable), (48 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_flag_get), (112 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_high), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_low), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_disable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_io23_output_disable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_io23_output_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_read_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_write_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_struct_para_init), (18 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_enable), (10 bytes).
    Removing gd32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_bootmode_config), (28 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_compensation_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_deinit), (20 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_enet_phy_interface_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exmc_swap_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exti_line_config), (172 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_flag_get), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_fmc_swap_config), (24 bytes).
    Removing gd32f4xx_timer.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_timer.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_auto_reload_shadow_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_auto_reload_shadow_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_autoreload_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_struct_para_init), (18 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_capture_value_register_read), (42 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_polarity_config), (70 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_state_config), (70 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_update_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_dma_request_source_select), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_clear_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_config), (492 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_fast_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_mode_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_polarity_config), (92 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config), (38 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_shadow_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_state_config), (92 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_remap_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_alignment), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_down_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_read), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_up_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_deinit), (388 bytes).
    Removing gd32f4xx_timer.o(i.timer_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_disable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_enable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_transfer_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_event_software_generate), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode0_config), (40 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config), (166 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_clear), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_get), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_hall_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_init), (152 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_capture_config), (326 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_pwm_capture_config), (356 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_trigger_source_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_clock_config), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_disable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_enable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_flag_clear), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_master_output_trigger_source_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_master_slave_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_output_value_selection_config), (34 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_config), (14 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_read), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_primary_output_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_quadrature_decoder_mode_config), (64 bytes).
    Removing gd32f4xx_timer.o(i.timer_repetition_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_single_pulse_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_slave_mode_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_struct_para_init), (22 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_source_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_write_chxval_register_config), (34 bytes).
    Removing gd32f4xx_tli.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_tli.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_init), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_current_pos_get), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_deinit), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_disable), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_dither_config), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_enable), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_flag_get), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_init), (188 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_disable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_enable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_init), (152 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_struct_para_init), (48 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_window_offset_modify), (228 bytes).
    Removing gd32f4xx_tli.o(i.tli_line_mark_set), (24 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_init), (28 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_struct_para_init), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_reload_config), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_struct_para_init), (34 bytes).
    Removing gd32f4xx_trng.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_trng.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_trng.o(i.trng_deinit), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_disable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_enable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_flag_get), (24 bytes).
    Removing gd32f4xx_trng.o(i.trng_get_true_random_data), (12 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_disable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_enable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_usart.o(i.usart_address_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_block_length_config), (28 bytes).
    Removing gd32f4xx_usart.o(i.usart_break_frame_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_data_first_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_dma_receive_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_dma_transmit_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_guard_time_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_cts_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_rts_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_disable), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_flag_clear), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_flag_get), (56 bytes).
    Removing gd32f4xx_usart.o(i.usart_invert_config), (104 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_lowpower_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_break_detection_length_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_wakeup_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_oversample_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_parity_check_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_parity_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_prescaler_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_disable), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_enable), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_threshold_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_sample_bit_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_send_break), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_autoretry_config), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_stop_bit_set), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_config), (34 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_word_length_set), (16 bytes).
    Removing gd32f4xx_wwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_config), (28 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_counter_update), (16 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_deinit), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_enable), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_clear), (12 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_get), (24 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_interrupt_enable), (20 bytes).
    Removing startup_gd32f450_470.o(HEAP), (1024 bytes).
    Removing diskio.o(.rev16_text), (4 bytes).
    Removing diskio.o(.revsh_text), (4 bytes).
    Removing ff.o(i.dir_read), (86 bytes).
    Removing ff.o(i.dir_remove), (44 bytes).
    Removing ff.o(i.f_chmod), (84 bytes).
    Removing ff.o(i.f_gets), (86 bytes).
    Removing ff.o(i.f_lseek), (432 bytes).
    Removing ff.o(i.f_printf), (700 bytes).
    Removing ff.o(i.f_putc), (50 bytes).
    Removing ff.o(i.f_puts), (42 bytes).
    Removing ff.o(i.f_readdir), (88 bytes).
    Removing ff.o(i.f_rename), (290 bytes).
    Removing ff.o(i.f_stat), (58 bytes).
    Removing ff.o(i.f_truncate), (156 bytes).
    Removing ff.o(i.f_unlink), (178 bytes).
    Removing ff.o(i.f_utime), (86 bytes).
    Removing ff.o(i.get_fileinfo), (152 bytes).

897 unused section(s) (total 67002 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isblank_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  iscntrl_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_c.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isxdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isupper_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalpha_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalnum_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_c.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ispunct_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isprint_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  islower_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isgraph_o.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strchr.c         0x00000000   Number         0  strchr.o ABSOLUTE
    ../clib/microlib/string/strcmp.c         0x00000000   Number         0  strcmp.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/string/strncmp.c        0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/microlib/string/strncpy.c        0x00000000   Number         0  strncpy.o ABSOLUTE
    ../clib/microlib/string/strstr.c         0x00000000   Number         0  strstr.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atoi.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtod.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_char.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_fp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  __0sscanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_int.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../mathlib/atof.c                        0x00000000   Number         0  atof.o ABSOLUTE
    ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s 0x00000000   Number         0  startup_gd32f450_470.o ABSOLUTE
    ..\CMSIS\GD\GD32F4xx\Source\system_gd32f4xx.c 0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    ..\Fatfs\diskio.c                        0x00000000   Number         0  diskio.o ABSOLUTE
    ..\Fatfs\ff.c                            0x00000000   Number         0  ff.o ABSOLUTE
    ..\Function\Function.c                   0x00000000   Number         0  function.o ABSOLUTE
    ..\HardWare\ADC.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ..\HardWare\LED\LED.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\HardWare\OLED.c                       0x00000000   Number         0  oled.o ABSOLUTE
    ..\HardWare\SDCARD\sdcard.c              0x00000000   Number         0  sdcard.o ABSOLUTE
    ..\HardWare\SPI_FLASH\SPI_FLASH.c        0x00000000   Number         0  spi_flash.o ABSOLUTE
    ..\HardWare\USART\USART.c                0x00000000   Number         0  usart.o ABSOLUTE
    ..\HardWare\key.c                        0x00000000   Number         0  key.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_adc.c 0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_can.c 0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_crc.c 0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_ctc.c 0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dac.c 0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dbg.c 0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dci.c 0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dma.c 0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_enet.c 0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_exmc.c 0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_exti.c 0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_fmc.c 0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_fwdgt.c 0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_gpio.c 0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_i2c.c 0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_ipa.c 0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_iref.c 0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_misc.c 0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_pmu.c 0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_rcu.c 0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_rtc.c 0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_sdio.c 0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_spi.c 0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_syscfg.c 0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_timer.c 0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_tli.c 0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_trng.c 0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_usart.c 0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_wwdgt.c 0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    ..\User\gd32f4xx_it.c                    0x00000000   Number         0  gd32f4xx_it.o ABSOLUTE
    ..\User\main.c                           0x00000000   Number         0  main.o ABSOLUTE
    ..\User\systick.c                        0x00000000   Number         0  systick.o ABSOLUTE
    ..\\CMSIS\\GD\\GD32F4xx\\Source\\system_gd32f4xx.c 0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    ..\\Fatfs\\diskio.c                      0x00000000   Number         0  diskio.o ABSOLUTE
    ..\\Function\\Function.c                 0x00000000   Number         0  function.o ABSOLUTE
    ..\\HardWare\\ADC.c                      0x00000000   Number         0  adc.o ABSOLUTE
    ..\\HardWare\\LED\\LED.c                 0x00000000   Number         0  led.o ABSOLUTE
    ..\\HardWare\\OLED.c                     0x00000000   Number         0  oled.o ABSOLUTE
    ..\\HardWare\\SDCARD\\sdcard.c           0x00000000   Number         0  sdcard.o ABSOLUTE
    ..\\HardWare\\SPI_FLASH\\SPI_FLASH.c     0x00000000   Number         0  spi_flash.o ABSOLUTE
    ..\\HardWare\\USART\\USART.c             0x00000000   Number         0  usart.o ABSOLUTE
    ..\\HardWare\\key.c                      0x00000000   Number         0  key.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_adc.c 0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_can.c 0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_crc.c 0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_ctc.c 0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dac.c 0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dbg.c 0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dci.c 0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dma.c 0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_enet.c 0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_exmc.c 0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_exti.c 0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_fmc.c 0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_fwdgt.c 0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_gpio.c 0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_i2c.c 0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_ipa.c 0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_iref.c 0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_misc.c 0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_pmu.c 0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_rcu.c 0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_rtc.c 0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_sdio.c 0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_spi.c 0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_syscfg.c 0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_timer.c 0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_tli.c 0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_trng.c 0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_usart.c 0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_wwdgt.c 0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    ..\\User\\gd32f4xx_it.c                  0x00000000   Number         0  gd32f4xx_it.o ABSOLUTE
    ..\\User\\main.c                         0x00000000   Number         0  main.o ABSOLUTE
    ..\\User\\systick.c                      0x00000000   Number         0  systick.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      428  startup_gd32f450_470.o(RESET)
    .ARM.Collect$$$$00000000                 0x080001ac   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080001ac   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080001b0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080001b4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080001b4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080001b4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x080001bc   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x080001bc   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x080001bc   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080001bc   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080001c0   Section       36  startup_gd32f450_470.o(.text)
    $v0                                      0x080001c0   Number         0  startup_gd32f450_470.o(.text)
    .text                                    0x080001e4   Section        0  memseta.o(.text)
    .text                                    0x08000208   Section        0  strstr.o(.text)
    .text                                    0x0800022c   Section        0  strncpy.o(.text)
    .text                                    0x08000244   Section        0  strchr.o(.text)
    .text                                    0x08000258   Section        0  strlen.o(.text)
    .text                                    0x08000266   Section        0  strcmp.o(.text)
    .text                                    0x08000284   Section        0  __0sscanf.o(.text)
    .text                                    0x080002bc   Section        0  _scanf_int.o(.text)
    .text                                    0x08000408   Section        0  atoi.o(.text)
    .text                                    0x08000422   Section        0  f2d.o(.text)
    .text                                    0x08000448   Section       48  cdcmple.o(.text)
    .text                                    0x08000478   Section       48  cdrcmple.o(.text)
    .text                                    0x080004a8   Section        0  d2f.o(.text)
    .text                                    0x080004e0   Section        0  uidiv.o(.text)
    .text                                    0x0800050c   Section        0  uldiv.o(.text)
    .text                                    0x0800056e   Section        0  _chval.o(.text)
    .text                                    0x0800058c   Section        0  scanf_char.o(.text)
    _scanf_char_input                        0x0800058d   Thumb Code    12  scanf_char.o(.text)
    .text                                    0x080005b4   Section        0  _sgetc.o(.text)
    .text                                    0x080005f4   Section        0  strtod.o(.text)
    _local_sscanf                            0x080005f5   Thumb Code    54  strtod.o(.text)
    .text                                    0x08000690   Section        0  strtol.o(.text)
    .text                                    0x08000700   Section        0  iusefp.o(.text)
    .text                                    0x08000700   Section        0  fepilogue.o(.text)
    .text                                    0x0800076e   Section        0  dadd.o(.text)
    .text                                    0x080008bc   Section        0  dmul.o(.text)
    .text                                    0x080009a0   Section        0  ddiv.o(.text)
    .text                                    0x08000a7e   Section        0  dfixul.o(.text)
    .text                                    0x08000ab0   Section       36  init.o(.text)
    .text                                    0x08000ad4   Section        0  llshl.o(.text)
    .text                                    0x08000af2   Section        0  llushr.o(.text)
    .text                                    0x08000b12   Section        0  llsshr.o(.text)
    .text                                    0x08000b38   Section        0  ctype_o.o(.text)
    .text                                    0x08000b40   Section        0  isspace_o.o(.text)
    .text                                    0x08000b54   Section        0  _scanf.o(.text)
    .text                                    0x08000e80   Section        0  scanf_fp.o(.text)
    _fp_value                                0x08000e81   Thumb Code   296  scanf_fp.o(.text)
    .text                                    0x080011e0   Section        0  _strtoul.o(.text)
    .text                                    0x0800127e   Section        0  depilogue.o(.text)
    .text                                    0x08001338   Section        0  dfltul.o(.text)
    i.ADC_Init                               0x08001350   Section        0  adc.o(i.ADC_Init)
    i.ADC_Read_Value                         0x080013ac   Section        0  adc.o(i.ADC_Read_Value)
    i.ADC_Read_Voltage                       0x080013dc   Section        0  adc.o(i.ADC_Read_Voltage)
    i.ADC_port_init                          0x08001420   Section        0  adc.o(i.ADC_port_init)
    i.BusFault_Handler                       0x0800145c   Section        0  gd32f4xx_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x08001460   Section        0  gd32f4xx_it.o(i.DebugMon_Handler)
    i.HardFault_Handler                      0x08001462   Section        0  gd32f4xx_it.o(i.HardFault_Handler)
    i.I2C_Start                              0x08001468   Section        0  oled.o(i.I2C_Start)
    i.I2C_Stop                               0x080014c0   Section        0  oled.o(i.I2C_Stop)
    i.I2C_WaitAck                            0x08001508   Section        0  oled.o(i.I2C_WaitAck)
    i.IIC_delay                              0x08001580   Section        0  oled.o(i.IIC_delay)
    i.KEY_Init                               0x08001590   Section        0  key.o(i.KEY_Init)
    i.LED_Init                               0x080015ac   Section        0  led.o(i.LED_Init)
    i.MemManage_Handler                      0x080015fc   Section        0  gd32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08001600   Section        0  gd32f4xx_it.o(i.NMI_Handler)
    i.NVIC_SetPriority                       0x08001604   Section        0  systick.o(i.NVIC_SetPriority)
    NVIC_SetPriority                         0x08001605   Thumb Code    32  systick.o(i.NVIC_SetPriority)
    i.OLED_Clear                             0x0800162c   Section        0  oled.o(i.OLED_Clear)
    i.OLED_ClearPoint                        0x0800165c   Section        0  oled.o(i.OLED_ClearPoint)
    i.OLED_DrawPoint                         0x080016bc   Section        0  oled.o(i.OLED_DrawPoint)
    i.OLED_Init                              0x080016f8   Section        0  oled.o(i.OLED_Init)
    i.OLED_Refresh                           0x08001840   Section        0  oled.o(i.OLED_Refresh)
    i.OLED_ShowChar                          0x0800188c   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowString                        0x08001988   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_WR_Byte                           0x080019ca   Section        0  oled.o(i.OLED_WR_Byte)
    i.PendSV_Handler                         0x08001a02   Section        0  gd32f4xx_it.o(i.PendSV_Handler)
    i.SDIO_IRQHandler                        0x08001a04   Section        0  gd32f4xx_it.o(i.SDIO_IRQHandler)
    i.SVC_Handler                            0x08001a0c   Section        0  gd32f4xx_it.o(i.SVC_Handler)
    i.Send_Byte                              0x08001a10   Section        0  oled.o(i.Send_Byte)
    i.SysTick_Handler                        0x08001a6c   Section        0  gd32f4xx_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08001a74   Section        0  system_gd32f4xx.o(i.SystemInit)
    i.System_Init                            0x08001b48   Section        0  function.o(i.System_Init)
    i.USART0_IRQHandler                      0x08001b50   Section        0  gd32f4xx_it.o(i.USART0_IRQHandler)
    i.UsageFault_Handler                     0x08001c3c   Section        0  gd32f4xx_it.o(i.UsageFault_Handler)
    i.UsrFunction                            0x08001c40   Section        0  function.o(i.UsrFunction)
    i.__0printf                              0x08002264   Section        0  printfa.o(i.__0printf)
    i.__0sprintf                             0x08002284   Section        0  printfa.o(i.__0sprintf)
    i.__aeabi_errno_addr                     0x080022ac   Section        0  errno.o(i.__aeabi_errno_addr)
    i.__hardfp_atof                          0x080022b8   Section        0  atof.o(i.__hardfp_atof)
    i.__read_errno                           0x080022f0   Section        0  errno.o(i.__read_errno)
    i.__scatterload_copy                     0x080022fc   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0800230a   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x0800230c   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x0800231c   Section        0  errno.o(i.__set_errno)
    i._fp_digits                             0x08002328   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x08002329   Thumb Code   366  printfa.o(i._fp_digits)
    i._is_digit                              0x080024ac   Section        0  scanf_fp.o(i._is_digit)
    i._printf_core                           0x080024bc   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x080024bd   Thumb Code  1744  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08002b98   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08002b99   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08002bbc   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08002bbd   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._sputc                                 0x08002bea   Section        0  printfa.o(i._sputc)
    _sputc                                   0x08002beb   Thumb Code    10  printfa.o(i._sputc)
    i.adc_calibration_enable                 0x08002bf4   Section        0  gd32f4xx_adc.o(i.adc_calibration_enable)
    i.adc_channel_length_config              0x08002c1e   Section        0  gd32f4xx_adc.o(i.adc_channel_length_config)
    i.adc_clock_config                       0x08002c70   Section        0  gd32f4xx_adc.o(i.adc_clock_config)
    i.adc_data_alignment_config              0x08002c94   Section        0  gd32f4xx_adc.o(i.adc_data_alignment_config)
    i.adc_deinit                             0x08002caa   Section        0  gd32f4xx_adc.o(i.adc_deinit)
    i.adc_enable                             0x08002cbe   Section        0  gd32f4xx_adc.o(i.adc_enable)
    i.adc_external_trigger_config            0x08002cd0   Section        0  gd32f4xx_adc.o(i.adc_external_trigger_config)
    i.adc_external_trigger_source_config     0x08002d04   Section        0  gd32f4xx_adc.o(i.adc_external_trigger_source_config)
    i.adc_flag_clear                         0x08002d34   Section        0  gd32f4xx_adc.o(i.adc_flag_clear)
    i.adc_flag_get                           0x08002d3c   Section        0  gd32f4xx_adc.o(i.adc_flag_get)
    i.adc_routine_channel_config             0x08002d4a   Section        0  gd32f4xx_adc.o(i.adc_routine_channel_config)
    i.adc_routine_data_read                  0x08002df6   Section        0  gd32f4xx_adc.o(i.adc_routine_data_read)
    i.adc_software_trigger_enable            0x08002dfe   Section        0  gd32f4xx_adc.o(i.adc_software_trigger_enable)
    i.adc_special_function_config            0x08002e22   Section        0  gd32f4xx_adc.o(i.adc_special_function_config)
    i.adjust_sample_cycle                    0x08002e7c   Section        0  function.o(i.adjust_sample_cycle)
    i.check_and_create_new_file              0x08002ee8   Section        0  function.o(i.check_and_create_new_file)
    i.check_directories_status               0x080031e8   Section        0  function.o(i.check_directories_status)
    i.check_fs                               0x0800349c   Section        0  ff.o(i.check_fs)
    check_fs                                 0x0800349d   Thumb Code   138  ff.o(i.check_fs)
    i.check_key1_press                       0x0800352c   Section        0  function.o(i.check_key1_press)
    i.check_key234_press                     0x08003598   Section        0  function.o(i.check_key234_press)
    i.chk_chr                                0x0800366c   Section        0  ff.o(i.chk_chr)
    chk_chr                                  0x0800366d   Thumb Code    20  ff.o(i.chk_chr)
    i.chk_mounted                            0x08003680   Section        0  ff.o(i.chk_mounted)
    chk_mounted                              0x08003681   Thumb Code   898  ff.o(i.chk_mounted)
    i.clust2sect                             0x08003a14   Section        0  ff.o(i.clust2sect)
    i.cmdsent_error_check                    0x08003a30   Section        0  sdcard.o(i.cmdsent_error_check)
    cmdsent_error_check                      0x08003a31   Thumb Code    40  sdcard.o(i.cmdsent_error_check)
    i.config_read_command                    0x08003a60   Section        0  function.o(i.config_read_command)
    i.config_save_command                    0x08003b30   Section        0  function.o(i.config_save_command)
    i.convert_to_bcd                         0x08003be8   Section        0  function.o(i.convert_to_bcd)
    i.create_chain                           0x08003c02   Section        0  ff.o(i.create_chain)
    create_chain                             0x08003c03   Thumb Code   202  ff.o(i.create_chain)
    i.create_directories                     0x08003ccc   Section        0  function.o(i.create_directories)
    i.create_name                            0x08003f6c   Section        0  ff.o(i.create_name)
    create_name                              0x08003f6d   Thumb Code   336  ff.o(i.create_name)
    i.delay_1ms                              0x080040cc   Section        0  systick.o(i.delay_1ms)
    i.delay_decrement                        0x080040e0   Section        0  systick.o(i.delay_decrement)
    i.dir_find                               0x080040f8   Section        0  ff.o(i.dir_find)
    dir_find                                 0x080040f9   Thumb Code    92  ff.o(i.dir_find)
    i.dir_next                               0x08004154   Section        0  ff.o(i.dir_next)
    dir_next                                 0x08004155   Thumb Code   280  ff.o(i.dir_next)
    i.dir_register                           0x0800426c   Section        0  ff.o(i.dir_register)
    dir_register                             0x0800426d   Thumb Code   110  ff.o(i.dir_register)
    i.dir_sdi                                0x080042da   Section        0  ff.o(i.dir_sdi)
    dir_sdi                                  0x080042db   Thumb Code   156  ff.o(i.dir_sdi)
    i.disk_initialize                        0x08004376   Section        0  diskio.o(i.disk_initialize)
    i.disk_ioctl                             0x080043fc   Section        0  diskio.o(i.disk_ioctl)
    i.disk_read                              0x08004402   Section        0  diskio.o(i.disk_read)
    i.disk_status                            0x08004452   Section        0  diskio.o(i.disk_status)
    i.disk_write                             0x0800445e   Section        0  diskio.o(i.disk_write)
    i.dma_channel_disable                    0x080044ae   Section        0  gd32f4xx_dma.o(i.dma_channel_disable)
    i.dma_channel_enable                     0x080044ce   Section        0  gd32f4xx_dma.o(i.dma_channel_enable)
    i.dma_channel_subperipheral_select       0x080044ee   Section        0  gd32f4xx_dma.o(i.dma_channel_subperipheral_select)
    i.dma_deinit                             0x08004514   Section        0  gd32f4xx_dma.o(i.dma_deinit)
    i.dma_flag_clear                         0x080045ba   Section        0  gd32f4xx_dma.o(i.dma_flag_clear)
    i.dma_flag_get                           0x080045f8   Section        0  gd32f4xx_dma.o(i.dma_flag_get)
    i.dma_flow_controller_config             0x08004644   Section        0  gd32f4xx_dma.o(i.dma_flow_controller_config)
    i.dma_multi_data_mode_init               0x08004684   Section        0  gd32f4xx_dma.o(i.dma_multi_data_mode_init)
    i.dma_receive_config                     0x080047e8   Section        0  sdcard.o(i.dma_receive_config)
    dma_receive_config                       0x080047e9   Thumb Code   170  sdcard.o(i.dma_receive_config)
    i.dma_transfer_config                    0x0800489c   Section        0  sdcard.o(i.dma_transfer_config)
    dma_transfer_config                      0x0800489d   Thumb Code   172  sdcard.o(i.dma_transfer_config)
    i.encode_voltage_hex                     0x08004950   Section        0  function.o(i.encode_voltage_hex)
    i.f_close                                0x080049ac   Section        0  ff.o(i.f_close)
    i.f_getfree                              0x080049c2   Section        0  ff.o(i.f_getfree)
    i.f_mkdir                                0x08004ad6   Section        0  ff.o(i.f_mkdir)
    i.f_mount                                0x08004c58   Section        0  ff.o(i.f_mount)
    i.f_open                                 0x08004c84   Section        0  ff.o(i.f_open)
    i.f_opendir                              0x08004df0   Section        0  ff.o(i.f_opendir)
    i.f_read                                 0x08004e5e   Section        0  ff.o(i.f_read)
    i.f_sync                                 0x0800502c   Section        0  ff.o(i.f_sync)
    i.f_write                                0x080050e4   Section        0  ff.o(i.f_write)
    i.follow_path                            0x080052f2   Section        0  ff.o(i.follow_path)
    follow_path                              0x080052f3   Thumb Code   158  ff.o(i.follow_path)
    i.fputc                                  0x08005390   Section        0  usart.o(i.fputc)
    i.gd_eval_com_init                       0x080053b4   Section        0  usart.o(i.gd_eval_com_init)
    i.generate_hide_filename                 0x08005450   Section        0  function.o(i.generate_hide_filename)
    i.generate_log_filename                  0x08005484   Section        0  function.o(i.generate_log_filename)
    i.generate_overlimit_filename            0x080054b0   Section        0  function.o(i.generate_overlimit_filename)
    i.generate_sample_filename               0x080054e8   Section        0  function.o(i.generate_sample_filename)
    i.get_current_time                       0x0800551c   Section        0  function.o(i.get_current_time)
    i.get_current_time_string                0x080055cc   Section        0  function.o(i.get_current_time_string)
    i.get_datetime_string                    0x08005620   Section        0  function.o(i.get_datetime_string)
    i.get_fat                                0x08005670   Section        0  ff.o(i.get_fat)
    i.get_fattime                            0x08005754   Section        0  diskio.o(i.get_fattime)
    i.get_unix_timestamp                     0x08005758   Section        0  function.o(i.get_unix_timestamp)
    i.gpio_af_set                            0x080057e4   Section        0  gd32f4xx_gpio.o(i.gpio_af_set)
    i.gpio_bit_reset                         0x08005842   Section        0  gd32f4xx_gpio.o(i.gpio_bit_reset)
    i.gpio_bit_set                           0x08005846   Section        0  gd32f4xx_gpio.o(i.gpio_bit_set)
    i.gpio_bit_toggle                        0x0800584a   Section        0  gd32f4xx_gpio.o(i.gpio_bit_toggle)
    i.gpio_config                            0x08005850   Section        0  sdcard.o(i.gpio_config)
    gpio_config                              0x08005851   Thumb Code   106  sdcard.o(i.gpio_config)
    i.gpio_input_bit_get                     0x080058c4   Section        0  gd32f4xx_gpio.o(i.gpio_input_bit_get)
    i.gpio_mode_set                          0x080058d4   Section        0  gd32f4xx_gpio.o(i.gpio_mode_set)
    i.gpio_output_options_set                0x08005922   Section        0  gd32f4xx_gpio.o(i.gpio_output_options_set)
    i.hide_command                           0x08005964   Section        0  function.o(i.hide_command)
    i.increment_boot_count                   0x08005a18   Section        0  function.o(i.increment_boot_count)
    i.init_data_storage                      0x08005a4c   Section        0  function.o(i.init_data_storage)
    i.load_config_from_flash                 0x08005bac   Section        0  function.o(i.load_config_from_flash)
    i.local_rtc_init                         0x08005cf4   Section        0  function.o(i.local_rtc_init)
    i.main                                   0x08005e28   Section        0  main.o(i.main)
    i.mem_cmp                                0x08005e36   Section        0  ff.o(i.mem_cmp)
    mem_cmp                                  0x08005e37   Thumb Code    38  ff.o(i.mem_cmp)
    i.mem_cpy                                0x08005e5c   Section        0  ff.o(i.mem_cpy)
    mem_cpy                                  0x08005e5d   Thumb Code    26  ff.o(i.mem_cpy)
    i.mem_set                                0x08005e76   Section        0  ff.o(i.mem_set)
    mem_set                                  0x08005e77   Thumb Code    20  ff.o(i.mem_set)
    i.move_window                            0x08005e8a   Section        0  ff.o(i.move_window)
    move_window                              0x08005e8b   Thumb Code   114  ff.o(i.move_window)
    i.nvic_config                            0x08005efc   Section        0  function.o(i.nvic_config)
    i.nvic_irq_enable                        0x08005f1c   Section        0  gd32f4xx_misc.o(i.nvic_irq_enable)
    i.nvic_priority_group_set                0x08005fe0   Section        0  gd32f4xx_misc.o(i.nvic_priority_group_set)
    i.parse_time_input                       0x08005ff4   Section        0  function.o(i.parse_time_input)
    i.pmu_backup_write_enable                0x080061c4   Section        0  gd32f4xx_pmu.o(i.pmu_backup_write_enable)
    i.put_fat                                0x080061d8   Section        0  ff.o(i.put_fat)
    i.r1_error_check                         0x08006310   Section        0  sdcard.o(i.r1_error_check)
    r1_error_check                           0x08006311   Thumb Code   120  sdcard.o(i.r1_error_check)
    i.r1_error_type_check                    0x08006394   Section        0  sdcard.o(i.r1_error_type_check)
    r1_error_type_check                      0x08006395   Thumb Code   174  sdcard.o(i.r1_error_type_check)
    i.r2_error_check                         0x08006444   Section        0  sdcard.o(i.r2_error_check)
    r2_error_check                           0x08006445   Thumb Code    70  sdcard.o(i.r2_error_check)
    i.r3_error_check                         0x08006494   Section        0  sdcard.o(i.r3_error_check)
    r3_error_check                           0x08006495   Thumb Code    52  sdcard.o(i.r3_error_check)
    i.r6_error_check                         0x080064d0   Section        0  sdcard.o(i.r6_error_check)
    r6_error_check                           0x080064d1   Thumb Code   158  sdcard.o(i.r6_error_check)
    i.r7_error_check                         0x08006578   Section        0  sdcard.o(i.r7_error_check)
    r7_error_check                           0x08006579   Thumb Code    74  sdcard.o(i.r7_error_check)
    i.rcu_bkp_reset_disable                  0x080065c8   Section        0  gd32f4xx_rcu.o(i.rcu_bkp_reset_disable)
    i.rcu_bkp_reset_enable                   0x080065dc   Section        0  gd32f4xx_rcu.o(i.rcu_bkp_reset_enable)
    i.rcu_clock_freq_get                     0x080065f0   Section        0  gd32f4xx_rcu.o(i.rcu_clock_freq_get)
    i.rcu_config                             0x08006714   Section        0  sdcard.o(i.rcu_config)
    rcu_config                               0x08006715   Thumb Code    36  sdcard.o(i.rcu_config)
    i.rcu_flag_get                           0x08006738   Section        0  gd32f4xx_rcu.o(i.rcu_flag_get)
    i.rcu_osci_on                            0x0800675c   Section        0  gd32f4xx_rcu.o(i.rcu_osci_on)
    i.rcu_osci_stab_wait                     0x08006780   Section        0  gd32f4xx_rcu.o(i.rcu_osci_stab_wait)
    i.rcu_periph_clock_enable                0x080068dc   Section        0  gd32f4xx_rcu.o(i.rcu_periph_clock_enable)
    i.rcu_periph_reset_disable               0x08006900   Section        0  gd32f4xx_rcu.o(i.rcu_periph_reset_disable)
    i.rcu_periph_reset_enable                0x08006924   Section        0  gd32f4xx_rcu.o(i.rcu_periph_reset_enable)
    i.rcu_rtc_clock_config                   0x08006948   Section        0  gd32f4xx_rcu.o(i.rcu_rtc_clock_config)
    i.read_adc_voltage                       0x08006960   Section        0  function.o(i.read_adc_voltage)
    i.read_config_from_tf                    0x08006968   Section        0  function.o(i.read_config_from_tf)
    i.remove_chain                           0x08006e50   Section        0  ff.o(i.remove_chain)
    remove_chain                             0x08006e51   Thumb Code   104  ff.o(i.remove_chain)
    i.rtc_config_command                     0x08006eb8   Section        0  function.o(i.rtc_config_command)
    i.rtc_current_time_get                   0x080071d8   Section        0  gd32f4xx_rtc.o(i.rtc_current_time_get)
    i.rtc_init                               0x0800723c   Section        0  gd32f4xx_rtc.o(i.rtc_init)
    i.rtc_init_mode_enter                    0x08007318   Section        0  gd32f4xx_rtc.o(i.rtc_init_mode_enter)
    i.rtc_init_mode_exit                     0x08007360   Section        0  gd32f4xx_rtc.o(i.rtc_init_mode_exit)
    i.rtc_now_command                        0x08007374   Section        0  function.o(i.rtc_now_command)
    i.rtc_register_sync_wait                 0x080073d0   Section        0  gd32f4xx_rtc.o(i.rtc_register_sync_wait)
    i.save_config_to_flash                   0x08007430   Section        0  function.o(i.save_config_to_flash)
    i.sd_block_read                          0x0800744c   Section        0  sdcard.o(i.sd_block_read)
    i.sd_block_write                         0x08007664   Section        0  sdcard.o(i.sd_block_write)
    i.sd_bus_mode_config                     0x08007984   Section        0  sdcard.o(i.sd_bus_mode_config)
    i.sd_bus_width_config                    0x08007a18   Section        0  sdcard.o(i.sd_bus_width_config)
    sd_bus_width_config                      0x08007a19   Thumb Code   242  sdcard.o(i.sd_bus_width_config)
    i.sd_card_information_get                0x08007b14   Section        0  sdcard.o(i.sd_card_information_get)
    i.sd_card_init                           0x08007dd4   Section        0  sdcard.o(i.sd_card_init)
    i.sd_card_select_deselect                0x08007ef0   Section        0  sdcard.o(i.sd_card_select_deselect)
    i.sd_card_state_get                      0x08007f18   Section        0  sdcard.o(i.sd_card_state_get)
    sd_card_state_get                        0x08007f19   Thumb Code   166  sdcard.o(i.sd_card_state_get)
    i.sd_cardstatus_get                      0x08007fd0   Section        0  sdcard.o(i.sd_cardstatus_get)
    i.sd_datablocksize_get                   0x08008018   Section        0  sdcard.o(i.sd_datablocksize_get)
    sd_datablocksize_get                     0x08008019   Thumb Code    24  sdcard.o(i.sd_datablocksize_get)
    i.sd_init                                0x08008030   Section        0  sdcard.o(i.sd_init)
    i.sd_interrupts_process                  0x08008078   Section        0  sdcard.o(i.sd_interrupts_process)
    i.sd_multiblocks_read                    0x080081a8   Section        0  sdcard.o(i.sd_multiblocks_read)
    i.sd_multiblocks_write                   0x08008444   Section        0  sdcard.o(i.sd_multiblocks_write)
    i.sd_power_on                            0x080087dc   Section        0  sdcard.o(i.sd_power_on)
    i.sd_scr_get                             0x08008908   Section        0  sdcard.o(i.sd_scr_get)
    sd_scr_get                               0x08008909   Thumb Code   344  sdcard.o(i.sd_scr_get)
    i.sd_transfer_mode_config                0x08008a64   Section        0  sdcard.o(i.sd_transfer_mode_config)
    i.sd_transfer_stop                       0x08008a7c   Section        0  sdcard.o(i.sd_transfer_stop)
    i.sdio_bus_mode_set                      0x08008aa0   Section        0  gd32f4xx_sdio.o(i.sdio_bus_mode_set)
    i.sdio_clock_config                      0x08008abc   Section        0  gd32f4xx_sdio.o(i.sdio_clock_config)
    i.sdio_clock_enable                      0x08008af0   Section        0  gd32f4xx_sdio.o(i.sdio_clock_enable)
    i.sdio_command_index_get                 0x08008b04   Section        0  gd32f4xx_sdio.o(i.sdio_command_index_get)
    i.sdio_command_response_config           0x08008b10   Section        0  gd32f4xx_sdio.o(i.sdio_command_response_config)
    i.sdio_csm_enable                        0x08008b48   Section        0  gd32f4xx_sdio.o(i.sdio_csm_enable)
    i.sdio_data_config                       0x08008b5c   Section        0  gd32f4xx_sdio.o(i.sdio_data_config)
    i.sdio_data_read                         0x08008b98   Section        0  gd32f4xx_sdio.o(i.sdio_data_read)
    i.sdio_data_transfer_config              0x08008ba4   Section        0  gd32f4xx_sdio.o(i.sdio_data_transfer_config)
    i.sdio_data_write                        0x08008bc0   Section        0  gd32f4xx_sdio.o(i.sdio_data_write)
    i.sdio_deinit                            0x08008bcc   Section        0  gd32f4xx_sdio.o(i.sdio_deinit)
    i.sdio_dma_disable                       0x08008be0   Section        0  gd32f4xx_sdio.o(i.sdio_dma_disable)
    i.sdio_dma_enable                        0x08008bf4   Section        0  gd32f4xx_sdio.o(i.sdio_dma_enable)
    i.sdio_dsm_disable                       0x08008c08   Section        0  gd32f4xx_sdio.o(i.sdio_dsm_disable)
    i.sdio_dsm_enable                        0x08008c1c   Section        0  gd32f4xx_sdio.o(i.sdio_dsm_enable)
    i.sdio_flag_clear                        0x08008c30   Section        0  gd32f4xx_sdio.o(i.sdio_flag_clear)
    i.sdio_flag_get                          0x08008c3c   Section        0  gd32f4xx_sdio.o(i.sdio_flag_get)
    i.sdio_hardware_clock_disable            0x08008c50   Section        0  gd32f4xx_sdio.o(i.sdio_hardware_clock_disable)
    i.sdio_interrupt_disable                 0x08008c64   Section        0  gd32f4xx_sdio.o(i.sdio_interrupt_disable)
    i.sdio_interrupt_enable                  0x08008c74   Section        0  gd32f4xx_sdio.o(i.sdio_interrupt_enable)
    i.sdio_interrupt_flag_clear              0x08008c84   Section        0  gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear)
    i.sdio_interrupt_flag_get                0x08008c90   Section        0  gd32f4xx_sdio.o(i.sdio_interrupt_flag_get)
    i.sdio_power_state_get                   0x08008ca4   Section        0  gd32f4xx_sdio.o(i.sdio_power_state_get)
    i.sdio_power_state_set                   0x08008cb0   Section        0  gd32f4xx_sdio.o(i.sdio_power_state_set)
    i.sdio_response_get                      0x08008cbc   Section        0  gd32f4xx_sdio.o(i.sdio_response_get)
    i.sdio_wait_type_set                     0x08008cf8   Section        0  gd32f4xx_sdio.o(i.sdio_wait_type_set)
    i.set_limit_value                        0x08008d14   Section        0  function.o(i.set_limit_value)
    i.set_ratio_value                        0x08008f0c   Section        0  function.o(i.set_ratio_value)
    i.spi_enable                             0x080090f4   Section        0  gd32f4xx_spi.o(i.spi_enable)
    i.spi_flash_buffer_read                  0x08009100   Section        0  spi_flash.o(i.spi_flash_buffer_read)
    i.spi_flash_buffer_write                 0x08009154   Section        0  spi_flash.o(i.spi_flash_buffer_write)
    i.spi_flash_init                         0x08009278   Section        0  spi_flash.o(i.spi_flash_init)
    i.spi_flash_page_write                   0x08009314   Section        0  spi_flash.o(i.spi_flash_page_write)
    i.spi_flash_read_id                      0x08009370   Section        0  spi_flash.o(i.spi_flash_read_id)
    i.spi_flash_sector_erase                 0x080093c4   Section        0  spi_flash.o(i.spi_flash_sector_erase)
    i.spi_flash_send_byte                    0x08009408   Section        0  spi_flash.o(i.spi_flash_send_byte)
    i.spi_flash_wait_for_write_end           0x08009440   Section        0  spi_flash.o(i.spi_flash_wait_for_write_end)
    i.spi_flash_write_enable                 0x08009478   Section        0  spi_flash.o(i.spi_flash_write_enable)
    i.spi_i2s_data_receive                   0x0800949c   Section        0  gd32f4xx_spi.o(i.spi_i2s_data_receive)
    i.spi_i2s_data_transmit                  0x080094a4   Section        0  gd32f4xx_spi.o(i.spi_i2s_data_transmit)
    i.spi_i2s_flag_get                       0x080094a8   Section        0  gd32f4xx_spi.o(i.spi_i2s_flag_get)
    i.spi_init                               0x080094b8   Section        0  gd32f4xx_spi.o(i.spi_init)
    i.start_sampling                         0x080094ec   Section        0  function.o(i.start_sampling)
    i.stop_sampling                          0x08009574   Section        0  function.o(i.stop_sampling)
    i.sync                                   0x0800964c   Section        0  ff.o(i.sync)
    sync                                     0x0800964d   Thumb Code   202  ff.o(i.sync)
    i.system_clock_240m_25m_hxtal            0x08009718   Section        0  system_gd32f4xx.o(i.system_clock_240m_25m_hxtal)
    system_clock_240m_25m_hxtal              0x08009719   Thumb Code   250  system_gd32f4xx.o(i.system_clock_240m_25m_hxtal)
    i.system_clock_config                    0x08009820   Section        0  system_gd32f4xx.o(i.system_clock_config)
    system_clock_config                      0x08009821   Thumb Code     8  system_gd32f4xx.o(i.system_clock_config)
    i.system_self_check                      0x08009828   Section        0  function.o(i.system_self_check)
    i.systick_config                         0x08009a0c   Section        0  systick.o(i.systick_config)
    i.toggle_sampling_by_key                 0x08009a5c   Section        0  function.o(i.toggle_sampling_by_key)
    i.unhide_command                         0x08009a74   Section        0  function.o(i.unhide_command)
    i.update_oled_display                    0x08009b30   Section        0  function.o(i.update_oled_display)
    i.update_sampling                        0x08009be0   Section        0  function.o(i.update_sampling)
    i.usart_baudrate_set                     0x08009ed0   Section        0  gd32f4xx_usart.o(i.usart_baudrate_set)
    i.usart_clear_buffer                     0x08009fb8   Section        0  usart.o(i.usart_clear_buffer)
    i.usart_data_receive                     0x08009fcc   Section        0  gd32f4xx_usart.o(i.usart_data_receive)
    i.usart_data_transmit                    0x08009fd6   Section        0  gd32f4xx_usart.o(i.usart_data_transmit)
    i.usart_deinit                           0x08009fe0   Section        0  gd32f4xx_usart.o(i.usart_deinit)
    i.usart_enable                           0x0800a0bc   Section        0  gd32f4xx_usart.o(i.usart_enable)
    i.usart_flag_clear                       0x0800a0c6   Section        0  gd32f4xx_usart.o(i.usart_flag_clear)
    i.usart_flag_get                         0x0800a0fa   Section        0  gd32f4xx_usart.o(i.usart_flag_get)
    i.usart_get_command                      0x0800a118   Section        0  usart.o(i.usart_get_command)
    i.usart_interrupt_enable                 0x0800a170   Section        0  gd32f4xx_usart.o(i.usart_interrupt_enable)
    i.usart_is_command_ready                 0x0800a18c   Section        0  usart.o(i.usart_is_command_ready)
    i.usart_receive_config                   0x0800a198   Section        0  gd32f4xx_usart.o(i.usart_receive_config)
    i.usart_transmit_config                  0x0800a1a8   Section        0  gd32f4xx_usart.o(i.usart_transmit_config)
    i.validate                               0x0800a1b8   Section        0  ff.o(i.validate)
    validate                                 0x0800a1b9   Thumb Code    42  ff.o(i.validate)
    i.write_hide_data                        0x0800a1e4   Section        0  function.o(i.write_hide_data)
    i.write_log_data                         0x0800a528   Section        0  function.o(i.write_log_data)
    i.write_overlimit_data                   0x0800a5c4   Section        0  function.o(i.write_overlimit_data)
    i.write_sample_data                      0x0800a82c   Section        0  function.o(i.write_sample_data)
    .constdata                               0x0800aa38   Section     8152  oled.o(.constdata)
    .constdata                               0x0800ca10   Section      129  ctype_o.o(.constdata)
    .constdata                               0x0800ca94   Section        4  ctype_o.o(.constdata)
    table                                    0x0800ca94   Data           4  ctype_o.o(.constdata)
    .conststring                             0x0800ca98   Section      174  function.o(.conststring)
    .data                                    0x20000000   Section        4  systick.o(.data)
    delay                                    0x20000000   Data           4  systick.o(.data)
    .data                                    0x20000004   Section        2  usart.o(.data)
    .data                                    0x20000008   Section       36  sdcard.o(.data)
    cardtype                                 0x20000010   Data           1  sdcard.o(.data)
    sd_rca                                   0x20000012   Data           2  sdcard.o(.data)
    transmode                                0x20000014   Data           4  sdcard.o(.data)
    totalnumber_bytes                        0x20000018   Data           4  sdcard.o(.data)
    stopcondition                            0x2000001c   Data           4  sdcard.o(.data)
    transerror                               0x20000020   Data           1  sdcard.o(.data)
    transend                                 0x20000024   Data           4  sdcard.o(.data)
    number_bytes                             0x20000028   Data           4  sdcard.o(.data)
    .data                                    0x2000002c   Section       80  function.o(.data)
    oled_update_counter                      0x20000078   Data           4  function.o(.data)
    .data                                    0x2000007c   Section        4  system_gd32f4xx.o(.data)
    .data                                    0x20000080   Section        6  ff.o(.data)
    FatFs                                    0x20000080   Data           4  ff.o(.data)
    Fsid                                     0x20000084   Data           2  ff.o(.data)
    .data                                    0x20000088   Section        4  stdout.o(.data)
    .data                                    0x2000008c   Section        4  errno.o(.data)
    _errno                                   0x2000008c   Data           4  errno.o(.data)
    .bss                                     0x20000090   Section       64  usart.o(.bss)
    .bss                                     0x200000d0   Section       32  sdcard.o(.bss)
    sd_csd                                   0x200000d0   Data          16  sdcard.o(.bss)
    sd_cid                                   0x200000e0   Data          16  sdcard.o(.bss)
    .bss                                     0x200000f0   Section      576  oled.o(.bss)
    .bss                                     0x20000330   Section     4076  function.o(.bss)
    STACK                                    0x20001320   Section     1024  startup_gd32f450_470.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    _scanf_longlong                           - Undefined Weak Reference
    _scanf_string                             - Undefined Weak Reference
    __Vectors_Size                           0x000001ac   Number         0  startup_gd32f450_470.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_gd32f450_470.o(RESET)
    __Vectors_End                            0x080001ac   Data           0  startup_gd32f450_470.o(RESET)
    __main                                   0x080001ad   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080001ad   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080001b1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080001b5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080001b5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080001b5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080001b5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x080001bd   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x080001bd   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x080001c1   Thumb Code     8  startup_gd32f450_470.o(.text)
    ADC_IRQHandler                           0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_EWMC_IRQHandler                     0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_RX0_IRQHandler                      0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_RX1_IRQHandler                      0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_TX_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_EWMC_IRQHandler                     0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_RX0_IRQHandler                      0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_RX1_IRQHandler                      0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_TX_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DCI_IRQHandler                           0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel0_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel1_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel2_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel3_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel4_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel5_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel6_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel7_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel0_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel1_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel2_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel4_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel5_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel6_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel7_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    ENET_IRQHandler                          0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    ENET_WKUP_IRQHandler                     0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXMC_IRQHandler                          0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI0_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI10_15_IRQHandler                     0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI1_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI2_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI3_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI4_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI5_9_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    FMC_IRQHandler                           0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    FPU_IRQHandler                           0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C0_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C0_EV_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C1_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C1_EV_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C2_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C2_EV_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    IPA_IRQHandler                           0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    LVD_IRQHandler                           0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    RCU_CTC_IRQHandler                       0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    RTC_Alarm_IRQHandler                     0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    RTC_WKUP_IRQHandler                      0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI0_IRQHandler                          0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI1_IRQHandler                          0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI2_IRQHandler                          0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI3_IRQHandler                          0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI4_IRQHandler                          0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI5_IRQHandler                          0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TAMPER_STAMP_IRQHandler                  0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_BRK_TIMER8_IRQHandler             0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_Channel_IRQHandler                0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_TRG_CMT_TIMER10_IRQHandler        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_UP_TIMER9_IRQHandler              0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER1_IRQHandler                        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER2_IRQHandler                        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER3_IRQHandler                        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER4_IRQHandler                        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER5_DAC_IRQHandler                    0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER6_IRQHandler                        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_BRK_TIMER11_IRQHandler            0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_Channel_IRQHandler                0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_TRG_CMT_TIMER13_IRQHandler        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_UP_TIMER12_IRQHandler             0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TLI_ER_IRQHandler                        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TLI_IRQHandler                           0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    TRNG_IRQHandler                          0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART3_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART4_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART6_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART7_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART1_IRQHandler                        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART2_IRQHandler                        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART5_IRQHandler                        0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBFS_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBFS_WKUP_IRQHandler                    0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_EP1_In_IRQHandler                  0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_EP1_Out_IRQHandler                 0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_WKUP_IRQHandler                    0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    WWDGT_IRQHandler                         0x080001db   Thumb Code     0  startup_gd32f450_470.o(.text)
    __aeabi_memset                           0x080001e5   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x080001e5   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x080001e5   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x080001f3   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x080001f3   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x080001f3   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x080001f7   Thumb Code    18  memseta.o(.text)
    strstr                                   0x08000209   Thumb Code    36  strstr.o(.text)
    strncpy                                  0x0800022d   Thumb Code    24  strncpy.o(.text)
    strchr                                   0x08000245   Thumb Code    20  strchr.o(.text)
    strlen                                   0x08000259   Thumb Code    14  strlen.o(.text)
    strcmp                                   0x08000267   Thumb Code    28  strcmp.o(.text)
    __0sscanf                                0x08000285   Thumb Code    48  __0sscanf.o(.text)
    _scanf_int                               0x080002bd   Thumb Code   332  _scanf_int.o(.text)
    atoi                                     0x08000409   Thumb Code    26  atoi.o(.text)
    __aeabi_f2d                              0x08000423   Thumb Code    38  f2d.o(.text)
    __aeabi_cdcmpeq                          0x08000449   Thumb Code     0  cdcmple.o(.text)
    __aeabi_cdcmple                          0x08000449   Thumb Code    48  cdcmple.o(.text)
    __aeabi_cdrcmple                         0x08000479   Thumb Code    48  cdrcmple.o(.text)
    __aeabi_d2f                              0x080004a9   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x080004e1   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x080004e1   Thumb Code    44  uidiv.o(.text)
    __aeabi_uldivmod                         0x0800050d   Thumb Code    98  uldiv.o(.text)
    _chval                                   0x0800056f   Thumb Code    28  _chval.o(.text)
    __vfscanf_char                           0x08000599   Thumb Code    20  scanf_char.o(.text)
    _sgetc                                   0x080005b5   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x080005d3   Thumb Code    34  _sgetc.o(.text)
    __strtod_int                             0x0800062b   Thumb Code    90  strtod.o(.text)
    strtol                                   0x08000691   Thumb Code   112  strtol.o(.text)
    __I$use$fp                               0x08000701   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x08000701   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x08000713   Thumb Code    92  fepilogue.o(.text)
    __aeabi_dadd                             0x0800076f   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x080008b1   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x080008b7   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x080008bd   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x080009a1   Thumb Code   222  ddiv.o(.text)
    __aeabi_d2ulz                            0x08000a7f   Thumb Code    48  dfixul.o(.text)
    __scatterload                            0x08000ab1   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000ab1   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x08000ad5   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08000ad5   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x08000af3   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000af3   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x08000b13   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08000b13   Thumb Code     0  llsshr.o(.text)
    __rt_ctype_table                         0x08000b39   Thumb Code     4  ctype_o.o(.text)
    isspace                                  0x08000b41   Thumb Code    18  isspace_o.o(.text)
    __vfscanf                                0x08000b55   Thumb Code   808  _scanf.o(.text)
    _scanf_real                              0x08000fa9   Thumb Code     0  scanf_fp.o(.text)
    _scanf_really_real                       0x08000fa9   Thumb Code   556  scanf_fp.o(.text)
    _strtoul                                 0x080011e1   Thumb Code   158  _strtoul.o(.text)
    _double_round                            0x0800127f   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x0800129d   Thumb Code   156  depilogue.o(.text)
    __aeabi_ul2d                             0x08001339   Thumb Code    24  dfltul.o(.text)
    ADC_Init                                 0x08001351   Thumb Code    86  adc.o(i.ADC_Init)
    ADC_Read_Value                           0x080013ad   Thumb Code    44  adc.o(i.ADC_Read_Value)
    ADC_Read_Voltage                         0x080013dd   Thumb Code    60  adc.o(i.ADC_Read_Voltage)
    ADC_port_init                            0x08001421   Thumb Code    52  adc.o(i.ADC_port_init)
    BusFault_Handler                         0x0800145d   Thumb Code     4  gd32f4xx_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x08001461   Thumb Code     2  gd32f4xx_it.o(i.DebugMon_Handler)
    HardFault_Handler                        0x08001463   Thumb Code     4  gd32f4xx_it.o(i.HardFault_Handler)
    I2C_Start                                0x08001469   Thumb Code    84  oled.o(i.I2C_Start)
    I2C_Stop                                 0x080014c1   Thumb Code    66  oled.o(i.I2C_Stop)
    I2C_WaitAck                              0x08001509   Thumb Code   116  oled.o(i.I2C_WaitAck)
    IIC_delay                                0x08001581   Thumb Code    16  oled.o(i.IIC_delay)
    KEY_Init                                 0x08001591   Thumb Code    24  key.o(i.KEY_Init)
    LED_Init                                 0x080015ad   Thumb Code    76  led.o(i.LED_Init)
    MemManage_Handler                        0x080015fd   Thumb Code     4  gd32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08001601   Thumb Code     2  gd32f4xx_it.o(i.NMI_Handler)
    OLED_Clear                               0x0800162d   Thumb Code    42  oled.o(i.OLED_Clear)
    OLED_ClearPoint                          0x0800165d   Thumb Code    90  oled.o(i.OLED_ClearPoint)
    OLED_DrawPoint                           0x080016bd   Thumb Code    54  oled.o(i.OLED_DrawPoint)
    OLED_Init                                0x080016f9   Thumb Code   324  oled.o(i.OLED_Init)
    OLED_Refresh                             0x08001841   Thumb Code    70  oled.o(i.OLED_Refresh)
    OLED_ShowChar                            0x0800188d   Thumb Code   240  oled.o(i.OLED_ShowChar)
    OLED_ShowString                          0x08001989   Thumb Code    66  oled.o(i.OLED_ShowString)
    OLED_WR_Byte                             0x080019cb   Thumb Code    56  oled.o(i.OLED_WR_Byte)
    PendSV_Handler                           0x08001a03   Thumb Code     2  gd32f4xx_it.o(i.PendSV_Handler)
    SDIO_IRQHandler                          0x08001a05   Thumb Code     8  gd32f4xx_it.o(i.SDIO_IRQHandler)
    SVC_Handler                              0x08001a0d   Thumb Code     2  gd32f4xx_it.o(i.SVC_Handler)
    Send_Byte                                0x08001a11   Thumb Code    88  oled.o(i.Send_Byte)
    SysTick_Handler                          0x08001a6d   Thumb Code     8  gd32f4xx_it.o(i.SysTick_Handler)
    SystemInit                               0x08001a75   Thumb Code   194  system_gd32f4xx.o(i.SystemInit)
    System_Init                              0x08001b49   Thumb Code     8  function.o(i.System_Init)
    USART0_IRQHandler                        0x08001b51   Thumb Code   174  gd32f4xx_it.o(i.USART0_IRQHandler)
    UsageFault_Handler                       0x08001c3d   Thumb Code     4  gd32f4xx_it.o(i.UsageFault_Handler)
    UsrFunction                              0x08001c41   Thumb Code  1508  function.o(i.UsrFunction)
    __0printf                                0x08002265   Thumb Code    22  printfa.o(i.__0printf)
    __1printf                                0x08002265   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x08002265   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x08002265   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x08002265   Thumb Code     0  printfa.o(i.__0printf)
    __0sprintf                               0x08002285   Thumb Code    34  printfa.o(i.__0sprintf)
    __1sprintf                               0x08002285   Thumb Code     0  printfa.o(i.__0sprintf)
    __2sprintf                               0x08002285   Thumb Code     0  printfa.o(i.__0sprintf)
    __c89sprintf                             0x08002285   Thumb Code     0  printfa.o(i.__0sprintf)
    sprintf                                  0x08002285   Thumb Code     0  printfa.o(i.__0sprintf)
    __aeabi_errno_addr                       0x080022ad   Thumb Code     4  errno.o(i.__aeabi_errno_addr)
    __rt_errno_addr                          0x080022ad   Thumb Code     0  errno.o(i.__aeabi_errno_addr)
    __hardfp_atof                            0x080022b9   Thumb Code    44  atof.o(i.__hardfp_atof)
    __read_errno                             0x080022f1   Thumb Code     6  errno.o(i.__read_errno)
    __scatterload_copy                       0x080022fd   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0800230b   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x0800230d   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x0800231d   Thumb Code     6  errno.o(i.__set_errno)
    _is_digit                                0x080024ad   Thumb Code    14  scanf_fp.o(i._is_digit)
    adc_calibration_enable                   0x08002bf5   Thumb Code    42  gd32f4xx_adc.o(i.adc_calibration_enable)
    adc_channel_length_config                0x08002c1f   Thumb Code    82  gd32f4xx_adc.o(i.adc_channel_length_config)
    adc_clock_config                         0x08002c71   Thumb Code    28  gd32f4xx_adc.o(i.adc_clock_config)
    adc_data_alignment_config                0x08002c95   Thumb Code    22  gd32f4xx_adc.o(i.adc_data_alignment_config)
    adc_deinit                               0x08002cab   Thumb Code    20  gd32f4xx_adc.o(i.adc_deinit)
    adc_enable                               0x08002cbf   Thumb Code    18  gd32f4xx_adc.o(i.adc_enable)
    adc_external_trigger_config              0x08002cd1   Thumb Code    52  gd32f4xx_adc.o(i.adc_external_trigger_config)
    adc_external_trigger_source_config       0x08002d05   Thumb Code    48  gd32f4xx_adc.o(i.adc_external_trigger_source_config)
    adc_flag_clear                           0x08002d35   Thumb Code     8  gd32f4xx_adc.o(i.adc_flag_clear)
    adc_flag_get                             0x08002d3d   Thumb Code    14  gd32f4xx_adc.o(i.adc_flag_get)
    adc_routine_channel_config               0x08002d4b   Thumb Code   172  gd32f4xx_adc.o(i.adc_routine_channel_config)
    adc_routine_data_read                    0x08002df7   Thumb Code     8  gd32f4xx_adc.o(i.adc_routine_data_read)
    adc_software_trigger_enable              0x08002dff   Thumb Code    36  gd32f4xx_adc.o(i.adc_software_trigger_enable)
    adc_special_function_config              0x08002e23   Thumb Code    90  gd32f4xx_adc.o(i.adc_special_function_config)
    adjust_sample_cycle                      0x08002e7d   Thumb Code    56  function.o(i.adjust_sample_cycle)
    check_and_create_new_file                0x08002ee9   Thumb Code   334  function.o(i.check_and_create_new_file)
    check_directories_status                 0x080031e9   Thumb Code   184  function.o(i.check_directories_status)
    check_key1_press                         0x0800352d   Thumb Code    90  function.o(i.check_key1_press)
    check_key234_press                       0x08003599   Thumb Code   184  function.o(i.check_key234_press)
    clust2sect                               0x08003a15   Thumb Code    26  ff.o(i.clust2sect)
    config_read_command                      0x08003a61   Thumb Code   102  function.o(i.config_read_command)
    config_save_command                      0x08003b31   Thumb Code    88  function.o(i.config_save_command)
    convert_to_bcd                           0x08003be9   Thumb Code    26  function.o(i.convert_to_bcd)
    create_directories                       0x08003ccd   Thumb Code   156  function.o(i.create_directories)
    delay_1ms                                0x080040cd   Thumb Code    16  systick.o(i.delay_1ms)
    delay_decrement                          0x080040e1   Thumb Code    18  systick.o(i.delay_decrement)
    disk_initialize                          0x08004377   Thumb Code   134  diskio.o(i.disk_initialize)
    disk_ioctl                               0x080043fd   Thumb Code     6  diskio.o(i.disk_ioctl)
    disk_read                                0x08004403   Thumb Code    80  diskio.o(i.disk_read)
    disk_status                              0x08004453   Thumb Code    12  diskio.o(i.disk_status)
    disk_write                               0x0800445f   Thumb Code    80  diskio.o(i.disk_write)
    dma_channel_disable                      0x080044af   Thumb Code    32  gd32f4xx_dma.o(i.dma_channel_disable)
    dma_channel_enable                       0x080044cf   Thumb Code    32  gd32f4xx_dma.o(i.dma_channel_enable)
    dma_channel_subperipheral_select         0x080044ef   Thumb Code    38  gd32f4xx_dma.o(i.dma_channel_subperipheral_select)
    dma_deinit                               0x08004515   Thumb Code   166  gd32f4xx_dma.o(i.dma_deinit)
    dma_flag_clear                           0x080045bb   Thumb Code    62  gd32f4xx_dma.o(i.dma_flag_clear)
    dma_flag_get                             0x080045f9   Thumb Code    76  gd32f4xx_dma.o(i.dma_flag_get)
    dma_flow_controller_config               0x08004645   Thumb Code    64  gd32f4xx_dma.o(i.dma_flow_controller_config)
    dma_multi_data_mode_init                 0x08004685   Thumb Code   352  gd32f4xx_dma.o(i.dma_multi_data_mode_init)
    encode_voltage_hex                       0x08004951   Thumb Code    74  function.o(i.encode_voltage_hex)
    f_close                                  0x080049ad   Thumb Code    22  ff.o(i.f_close)
    f_getfree                                0x080049c3   Thumb Code   276  ff.o(i.f_getfree)
    f_mkdir                                  0x08004ad7   Thumb Code   384  ff.o(i.f_mkdir)
    f_mount                                  0x08004c59   Thumb Code    38  ff.o(i.f_mount)
    f_open                                   0x08004c85   Thumb Code   364  ff.o(i.f_open)
    f_opendir                                0x08004df1   Thumb Code   110  ff.o(i.f_opendir)
    f_read                                   0x08004e5f   Thumb Code   462  ff.o(i.f_read)
    f_sync                                   0x0800502d   Thumb Code   184  ff.o(i.f_sync)
    f_write                                  0x080050e5   Thumb Code   526  ff.o(i.f_write)
    fputc                                    0x08005391   Thumb Code    32  usart.o(i.fputc)
    gd_eval_com_init                         0x080053b5   Thumb Code   148  usart.o(i.gd_eval_com_init)
    generate_hide_filename                   0x08005451   Thumb Code    22  function.o(i.generate_hide_filename)
    generate_log_filename                    0x08005485   Thumb Code    20  function.o(i.generate_log_filename)
    generate_overlimit_filename              0x080054b1   Thumb Code    22  function.o(i.generate_overlimit_filename)
    generate_sample_filename                 0x080054e9   Thumb Code    22  function.o(i.generate_sample_filename)
    get_current_time                         0x0800551d   Thumb Code   170  function.o(i.get_current_time)
    get_current_time_string                  0x080055cd   Thumb Code    48  function.o(i.get_current_time_string)
    get_datetime_string                      0x08005621   Thumb Code    48  function.o(i.get_datetime_string)
    get_fat                                  0x08005671   Thumb Code   228  ff.o(i.get_fat)
    get_fattime                              0x08005755   Thumb Code     4  diskio.o(i.get_fattime)
    get_unix_timestamp                       0x08005759   Thumb Code   118  function.o(i.get_unix_timestamp)
    gpio_af_set                              0x080057e5   Thumb Code    94  gd32f4xx_gpio.o(i.gpio_af_set)
    gpio_bit_reset                           0x08005843   Thumb Code     4  gd32f4xx_gpio.o(i.gpio_bit_reset)
    gpio_bit_set                             0x08005847   Thumb Code     4  gd32f4xx_gpio.o(i.gpio_bit_set)
    gpio_bit_toggle                          0x0800584b   Thumb Code     4  gd32f4xx_gpio.o(i.gpio_bit_toggle)
    gpio_input_bit_get                       0x080058c5   Thumb Code    16  gd32f4xx_gpio.o(i.gpio_input_bit_get)
    gpio_mode_set                            0x080058d5   Thumb Code    78  gd32f4xx_gpio.o(i.gpio_mode_set)
    gpio_output_options_set                  0x08005923   Thumb Code    66  gd32f4xx_gpio.o(i.gpio_output_options_set)
    hide_command                             0x08005965   Thumb Code    44  function.o(i.hide_command)
    increment_boot_count                     0x08005a19   Thumb Code    28  function.o(i.increment_boot_count)
    init_data_storage                        0x08005a4d   Thumb Code   124  function.o(i.init_data_storage)
    load_config_from_flash                   0x08005bad   Thumb Code   246  function.o(i.load_config_from_flash)
    local_rtc_init                           0x08005cf5   Thumb Code   124  function.o(i.local_rtc_init)
    main                                     0x08005e29   Thumb Code    14  main.o(i.main)
    nvic_config                              0x08005efd   Thumb Code    32  function.o(i.nvic_config)
    nvic_irq_enable                          0x08005f1d   Thumb Code   186  gd32f4xx_misc.o(i.nvic_irq_enable)
    nvic_priority_group_set                  0x08005fe1   Thumb Code    10  gd32f4xx_misc.o(i.nvic_priority_group_set)
    parse_time_input                         0x08005ff5   Thumb Code   404  function.o(i.parse_time_input)
    pmu_backup_write_enable                  0x080061c5   Thumb Code    14  gd32f4xx_pmu.o(i.pmu_backup_write_enable)
    put_fat                                  0x080061d9   Thumb Code   310  ff.o(i.put_fat)
    rcu_bkp_reset_disable                    0x080065c9   Thumb Code    14  gd32f4xx_rcu.o(i.rcu_bkp_reset_disable)
    rcu_bkp_reset_enable                     0x080065dd   Thumb Code    14  gd32f4xx_rcu.o(i.rcu_bkp_reset_enable)
    rcu_clock_freq_get                       0x080065f1   Thumb Code   264  gd32f4xx_rcu.o(i.rcu_clock_freq_get)
    rcu_flag_get                             0x08006739   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_flag_get)
    rcu_osci_on                              0x0800675d   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_osci_on)
    rcu_osci_stab_wait                       0x08006781   Thumb Code   342  gd32f4xx_rcu.o(i.rcu_osci_stab_wait)
    rcu_periph_clock_enable                  0x080068dd   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_clock_enable)
    rcu_periph_reset_disable                 0x08006901   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_reset_disable)
    rcu_periph_reset_enable                  0x08006925   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_reset_enable)
    rcu_rtc_clock_config                     0x08006949   Thumb Code    18  gd32f4xx_rcu.o(i.rcu_rtc_clock_config)
    read_adc_voltage                         0x08006961   Thumb Code     8  function.o(i.read_adc_voltage)
    read_config_from_tf                      0x08006969   Thumb Code   954  function.o(i.read_config_from_tf)
    rtc_config_command                       0x08006eb9   Thumb Code   348  function.o(i.rtc_config_command)
    rtc_current_time_get                     0x080071d9   Thumb Code    96  gd32f4xx_rtc.o(i.rtc_current_time_get)
    rtc_init                                 0x0800723d   Thumb Code   216  gd32f4xx_rtc.o(i.rtc_init)
    rtc_init_mode_enter                      0x08007319   Thumb Code    66  gd32f4xx_rtc.o(i.rtc_init_mode_enter)
    rtc_init_mode_exit                       0x08007361   Thumb Code    14  gd32f4xx_rtc.o(i.rtc_init_mode_exit)
    rtc_now_command                          0x08007375   Thumb Code    40  function.o(i.rtc_now_command)
    rtc_register_sync_wait                   0x080073d1   Thumb Code    92  gd32f4xx_rtc.o(i.rtc_register_sync_wait)
    save_config_to_flash                     0x08007431   Thumb Code    22  function.o(i.save_config_to_flash)
    sd_block_read                            0x0800744d   Thumb Code   500  sdcard.o(i.sd_block_read)
    sd_block_write                           0x08007665   Thumb Code   760  sdcard.o(i.sd_block_write)
    sd_bus_mode_config                       0x08007985   Thumb Code   144  sdcard.o(i.sd_bus_mode_config)
    sd_card_information_get                  0x08007b15   Thumb Code   686  sdcard.o(i.sd_card_information_get)
    sd_card_init                             0x08007dd5   Thumb Code   268  sdcard.o(i.sd_card_init)
    sd_card_select_deselect                  0x08007ef1   Thumb Code    38  sdcard.o(i.sd_card_select_deselect)
    sd_cardstatus_get                        0x08007fd1   Thumb Code    66  sdcard.o(i.sd_cardstatus_get)
    sd_init                                  0x08008031   Thumb Code    70  sdcard.o(i.sd_init)
    sd_interrupts_process                    0x08008079   Thumb Code   286  sdcard.o(i.sd_interrupts_process)
    sd_multiblocks_read                      0x080081a9   Thumb Code   632  sdcard.o(i.sd_multiblocks_read)
    sd_multiblocks_write                     0x08008445   Thumb Code   878  sdcard.o(i.sd_multiblocks_write)
    sd_power_on                              0x080087dd   Thumb Code   290  sdcard.o(i.sd_power_on)
    sd_transfer_mode_config                  0x08008a65   Thumb Code    20  sdcard.o(i.sd_transfer_mode_config)
    sd_transfer_stop                         0x08008a7d   Thumb Code    36  sdcard.o(i.sd_transfer_stop)
    sdio_bus_mode_set                        0x08008aa1   Thumb Code    22  gd32f4xx_sdio.o(i.sdio_bus_mode_set)
    sdio_clock_config                        0x08008abd   Thumb Code    44  gd32f4xx_sdio.o(i.sdio_clock_config)
    sdio_clock_enable                        0x08008af1   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_clock_enable)
    sdio_command_index_get                   0x08008b05   Thumb Code     8  gd32f4xx_sdio.o(i.sdio_command_index_get)
    sdio_command_response_config             0x08008b11   Thumb Code    52  gd32f4xx_sdio.o(i.sdio_command_response_config)
    sdio_csm_enable                          0x08008b49   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_csm_enable)
    sdio_data_config                         0x08008b5d   Thumb Code    54  gd32f4xx_sdio.o(i.sdio_data_config)
    sdio_data_read                           0x08008b99   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_data_read)
    sdio_data_transfer_config                0x08008ba5   Thumb Code    24  gd32f4xx_sdio.o(i.sdio_data_transfer_config)
    sdio_data_write                          0x08008bc1   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_data_write)
    sdio_deinit                              0x08008bcd   Thumb Code    20  gd32f4xx_sdio.o(i.sdio_deinit)
    sdio_dma_disable                         0x08008be1   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_dma_disable)
    sdio_dma_enable                          0x08008bf5   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_dma_enable)
    sdio_dsm_disable                         0x08008c09   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_dsm_disable)
    sdio_dsm_enable                          0x08008c1d   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_dsm_enable)
    sdio_flag_clear                          0x08008c31   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_flag_clear)
    sdio_flag_get                            0x08008c3d   Thumb Code    16  gd32f4xx_sdio.o(i.sdio_flag_get)
    sdio_hardware_clock_disable              0x08008c51   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_hardware_clock_disable)
    sdio_interrupt_disable                   0x08008c65   Thumb Code    12  gd32f4xx_sdio.o(i.sdio_interrupt_disable)
    sdio_interrupt_enable                    0x08008c75   Thumb Code    12  gd32f4xx_sdio.o(i.sdio_interrupt_enable)
    sdio_interrupt_flag_clear                0x08008c85   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear)
    sdio_interrupt_flag_get                  0x08008c91   Thumb Code    16  gd32f4xx_sdio.o(i.sdio_interrupt_flag_get)
    sdio_power_state_get                     0x08008ca5   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_power_state_get)
    sdio_power_state_set                     0x08008cb1   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_power_state_set)
    sdio_response_get                        0x08008cbd   Thumb Code    56  gd32f4xx_sdio.o(i.sdio_response_get)
    sdio_wait_type_set                       0x08008cf9   Thumb Code    22  gd32f4xx_sdio.o(i.sdio_wait_type_set)
    set_limit_value                          0x08008d15   Thumb Code   340  function.o(i.set_limit_value)
    set_ratio_value                          0x08008f0d   Thumb Code   340  function.o(i.set_ratio_value)
    spi_enable                               0x080090f5   Thumb Code    10  gd32f4xx_spi.o(i.spi_enable)
    spi_flash_buffer_read                    0x08009101   Thumb Code    80  spi_flash.o(i.spi_flash_buffer_read)
    spi_flash_buffer_write                   0x08009155   Thumb Code   290  spi_flash.o(i.spi_flash_buffer_write)
    spi_flash_init                           0x08009279   Thumb Code   146  spi_flash.o(i.spi_flash_init)
    spi_flash_page_write                     0x08009315   Thumb Code    86  spi_flash.o(i.spi_flash_page_write)
    spi_flash_read_id                        0x08009371   Thumb Code    78  spi_flash.o(i.spi_flash_read_id)
    spi_flash_sector_erase                   0x080093c5   Thumb Code    62  spi_flash.o(i.spi_flash_sector_erase)
    spi_flash_send_byte                      0x08009409   Thumb Code    50  spi_flash.o(i.spi_flash_send_byte)
    spi_flash_wait_for_write_end             0x08009441   Thumb Code    50  spi_flash.o(i.spi_flash_wait_for_write_end)
    spi_flash_write_enable                   0x08009479   Thumb Code    30  spi_flash.o(i.spi_flash_write_enable)
    spi_i2s_data_receive                     0x0800949d   Thumb Code     8  gd32f4xx_spi.o(i.spi_i2s_data_receive)
    spi_i2s_data_transmit                    0x080094a5   Thumb Code     4  gd32f4xx_spi.o(i.spi_i2s_data_transmit)
    spi_i2s_flag_get                         0x080094a9   Thumb Code    16  gd32f4xx_spi.o(i.spi_i2s_flag_get)
    spi_init                                 0x080094b9   Thumb Code    50  gd32f4xx_spi.o(i.spi_init)
    start_sampling                           0x080094ed   Thumb Code    52  function.o(i.start_sampling)
    stop_sampling                            0x08009575   Thumb Code    76  function.o(i.stop_sampling)
    system_self_check                        0x08009829   Thumb Code   176  function.o(i.system_self_check)
    systick_config                           0x08009a0d   Thumb Code    74  systick.o(i.systick_config)
    toggle_sampling_by_key                   0x08009a5d   Thumb Code    20  function.o(i.toggle_sampling_by_key)
    unhide_command                           0x08009a75   Thumb Code    44  function.o(i.unhide_command)
    update_oled_display                      0x08009b31   Thumb Code   132  function.o(i.update_oled_display)
    update_sampling                          0x08009be1   Thumb Code   484  function.o(i.update_sampling)
    usart_baudrate_set                       0x08009ed1   Thumb Code   224  gd32f4xx_usart.o(i.usart_baudrate_set)
    usart_clear_buffer                       0x08009fb9   Thumb Code    12  usart.o(i.usart_clear_buffer)
    usart_data_receive                       0x08009fcd   Thumb Code    10  gd32f4xx_usart.o(i.usart_data_receive)
    usart_data_transmit                      0x08009fd7   Thumb Code     8  gd32f4xx_usart.o(i.usart_data_transmit)
    usart_deinit                             0x08009fe1   Thumb Code   210  gd32f4xx_usart.o(i.usart_deinit)
    usart_enable                             0x0800a0bd   Thumb Code    10  gd32f4xx_usart.o(i.usart_enable)
    usart_flag_clear                         0x0800a0c7   Thumb Code    52  gd32f4xx_usart.o(i.usart_flag_clear)
    usart_flag_get                           0x0800a0fb   Thumb Code    30  gd32f4xx_usart.o(i.usart_flag_get)
    usart_get_command                        0x0800a119   Thumb Code    74  usart.o(i.usart_get_command)
    usart_interrupt_enable                   0x0800a171   Thumb Code    26  gd32f4xx_usart.o(i.usart_interrupt_enable)
    usart_is_command_ready                   0x0800a18d   Thumb Code     6  usart.o(i.usart_is_command_ready)
    usart_receive_config                     0x0800a199   Thumb Code    16  gd32f4xx_usart.o(i.usart_receive_config)
    usart_transmit_config                    0x0800a1a9   Thumb Code    16  gd32f4xx_usart.o(i.usart_transmit_config)
    write_hide_data                          0x0800a1e5   Thumb Code   342  function.o(i.write_hide_data)
    write_log_data                           0x0800a529   Thumb Code    94  function.o(i.write_log_data)
    write_overlimit_data                     0x0800a5c5   Thumb Code   262  function.o(i.write_overlimit_data)
    write_sample_data                        0x0800a82d   Thumb Code   220  function.o(i.write_sample_data)
    F6x8                                     0x0800aa38   Data         552  oled.o(.constdata)
    F8X16                                    0x0800ac60   Data        1520  oled.o(.constdata)
    asc2_1206                                0x0800b250   Data        1140  oled.o(.constdata)
    asc2_1608                                0x0800b6c4   Data        1520  oled.o(.constdata)
    asc2_2412                                0x0800bcb4   Data        3420  oled.o(.constdata)
    __ctype_table                            0x0800ca10   Data         129  ctype_o.o(.constdata)
    Region$$Table$$Base                      0x0800cb48   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800cb68   Number         0  anon$$obj.o(Region$$Table)
    usart_rx_index                           0x20000004   Data           1  usart.o(.data)
    usart_cmd_ready                          0x20000005   Data           1  usart.o(.data)
    sd_scr                                   0x20000008   Data           8  sdcard.o(.data)
    flash_id                                 0x2000002c   Data           4  function.o(.data)
    i                                        0x20000030   Data           2  function.o(.data)
    count                                    0x20000032   Data           2  function.o(.data)
    result                                   0x20000034   Data           2  function.o(.data)
    is_successful                            0x20000036   Data           1  function.o(.data)
    config_data                              0x20000038   Data          16  function.o(.data)
    sampling_active                          0x20000048   Data           1  function.o(.data)
    sample_cycle                             0x2000004c   Data           4  function.o(.data)
    led_blink_counter                        0x20000050   Data           4  function.o(.data)
    led_state                                0x20000054   Data           1  function.o(.data)
    last_sample_second                       0x20000055   Data           1  function.o(.data)
    hide_mode                                0x20000056   Data           1  function.o(.data)
    key1_last_state                          0x20000057   Data           1  function.o(.data)
    key2_last_state                          0x20000058   Data           1  function.o(.data)
    key3_last_state                          0x20000059   Data           1  function.o(.data)
    key4_last_state                          0x2000005a   Data           1  function.o(.data)
    key_debounce_counter                     0x2000005c   Data           4  function.o(.data)
    key1_pressed_flag                        0x20000060   Data           1  function.o(.data)
    key2_pressed_flag                        0x20000061   Data           1  function.o(.data)
    key3_pressed_flag                        0x20000062   Data           1  function.o(.data)
    key4_pressed_flag                        0x20000063   Data           1  function.o(.data)
    key_check_counter                        0x20000064   Data           4  function.o(.data)
    br                                       0x20000068   Data           4  function.o(.data)
    bw                                       0x2000006c   Data           4  function.o(.data)
    current_time                             0x20000070   Data           6  function.o(.data)
    SystemCoreClock                          0x2000007c   Data           4  system_gd32f4xx.o(.data)
    __stdout                                 0x20000088   Data           4  stdout.o(.data)
    usart_rx_buffer                          0x20000090   Data          64  usart.o(.bss)
    OLED_GRAM                                0x200000f0   Data         576  oled.o(.bss)
    tx_buffer                                0x20000330   Data         256  function.o(.bss)
    rx_buffer                                0x20000430   Data         256  function.o(.bss)
    file_manager                             0x20000530   Data        2200  function.o(.bss)
    fdst                                     0x20000dc8   Data         548  function.o(.bss)
    fs                                       0x20000fec   Data         560  function.o(.bss)
    buffer                                   0x2000121c   Data         128  function.o(.bss)
    filebuffer                               0x2000129c   Data         128  function.o(.bss)
    __initial_sp                             0x20001720   Data           0  startup_gd32f450_470.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001ad

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000cbf8, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000cb68, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001ac   Data   RO         6853    RESET               startup_gd32f450_470.o
    0x080001ac   0x080001ac   0x00000000   Code   RO         7176  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080001ac   0x080001ac   0x00000004   Code   RO         7472    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080001b0   0x080001b0   0x00000004   Code   RO         7475    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         7477    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         7479    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080001b4   0x080001b4   0x00000008   Code   RO         7480    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080001bc   0x080001bc   0x00000000   Code   RO         7482    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x080001bc   0x080001bc   0x00000000   Code   RO         7484    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x080001bc   0x080001bc   0x00000004   Code   RO         7473    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001c0   0x080001c0   0x00000024   Code   RO         6854    .text               startup_gd32f450_470.o
    0x080001e4   0x080001e4   0x00000024   Code   RO         7181    .text               mc_w.l(memseta.o)
    0x08000208   0x08000208   0x00000024   Code   RO         7183    .text               mc_w.l(strstr.o)
    0x0800022c   0x0800022c   0x00000018   Code   RO         7185    .text               mc_w.l(strncpy.o)
    0x08000244   0x08000244   0x00000014   Code   RO         7187    .text               mc_w.l(strchr.o)
    0x08000258   0x08000258   0x0000000e   Code   RO         7189    .text               mc_w.l(strlen.o)
    0x08000266   0x08000266   0x0000001c   Code   RO         7191    .text               mc_w.l(strcmp.o)
    0x08000282   0x08000282   0x00000002   PAD
    0x08000284   0x08000284   0x00000038   Code   RO         7458    .text               mc_w.l(__0sscanf.o)
    0x080002bc   0x080002bc   0x0000014c   Code   RO         7460    .text               mc_w.l(_scanf_int.o)
    0x08000408   0x08000408   0x0000001a   Code   RO         7462    .text               mc_w.l(atoi.o)
    0x08000422   0x08000422   0x00000026   Code   RO         7464    .text               mf_w.l(f2d.o)
    0x08000448   0x08000448   0x00000030   Code   RO         7466    .text               mf_w.l(cdcmple.o)
    0x08000478   0x08000478   0x00000030   Code   RO         7468    .text               mf_w.l(cdrcmple.o)
    0x080004a8   0x080004a8   0x00000038   Code   RO         7470    .text               mf_w.l(d2f.o)
    0x080004e0   0x080004e0   0x0000002c   Code   RO         7487    .text               mc_w.l(uidiv.o)
    0x0800050c   0x0800050c   0x00000062   Code   RO         7489    .text               mc_w.l(uldiv.o)
    0x0800056e   0x0800056e   0x0000001c   Code   RO         7498    .text               mc_w.l(_chval.o)
    0x0800058a   0x0800058a   0x00000002   PAD
    0x0800058c   0x0800058c   0x00000028   Code   RO         7500    .text               mc_w.l(scanf_char.o)
    0x080005b4   0x080005b4   0x00000040   Code   RO         7502    .text               mc_w.l(_sgetc.o)
    0x080005f4   0x080005f4   0x0000009c   Code   RO         7504    .text               mc_w.l(strtod.o)
    0x08000690   0x08000690   0x00000070   Code   RO         7506    .text               mc_w.l(strtol.o)
    0x08000700   0x08000700   0x00000000   Code   RO         7508    .text               mc_w.l(iusefp.o)
    0x08000700   0x08000700   0x0000006e   Code   RO         7509    .text               mf_w.l(fepilogue.o)
    0x0800076e   0x0800076e   0x0000014e   Code   RO         7511    .text               mf_w.l(dadd.o)
    0x080008bc   0x080008bc   0x000000e4   Code   RO         7513    .text               mf_w.l(dmul.o)
    0x080009a0   0x080009a0   0x000000de   Code   RO         7515    .text               mf_w.l(ddiv.o)
    0x08000a7e   0x08000a7e   0x00000030   Code   RO         7517    .text               mf_w.l(dfixul.o)
    0x08000aae   0x08000aae   0x00000002   PAD
    0x08000ab0   0x08000ab0   0x00000024   Code   RO         7519    .text               mc_w.l(init.o)
    0x08000ad4   0x08000ad4   0x0000001e   Code   RO         7521    .text               mc_w.l(llshl.o)
    0x08000af2   0x08000af2   0x00000020   Code   RO         7523    .text               mc_w.l(llushr.o)
    0x08000b12   0x08000b12   0x00000024   Code   RO         7525    .text               mc_w.l(llsshr.o)
    0x08000b36   0x08000b36   0x00000002   PAD
    0x08000b38   0x08000b38   0x00000008   Code   RO         7529    .text               mc_w.l(ctype_o.o)
    0x08000b40   0x08000b40   0x00000012   Code   RO         7551    .text               mc_w.l(isspace_o.o)
    0x08000b52   0x08000b52   0x00000002   PAD
    0x08000b54   0x08000b54   0x0000032c   Code   RO         7557    .text               mc_w.l(_scanf.o)
    0x08000e80   0x08000e80   0x00000360   Code   RO         7559    .text               mc_w.l(scanf_fp.o)
    0x080011e0   0x080011e0   0x0000009e   Code   RO         7563    .text               mc_w.l(_strtoul.o)
    0x0800127e   0x0800127e   0x000000ba   Code   RO         7565    .text               mf_w.l(depilogue.o)
    0x08001338   0x08001338   0x00000018   Code   RO         7570    .text               mf_w.l(dfltul.o)
    0x08001350   0x08001350   0x0000005c   Code   RO          696    i.ADC_Init          adc.o
    0x080013ac   0x080013ac   0x00000030   Code   RO          697    i.ADC_Read_Value    adc.o
    0x080013dc   0x080013dc   0x00000044   Code   RO          698    i.ADC_Read_Voltage  adc.o
    0x08001420   0x08001420   0x0000003c   Code   RO          699    i.ADC_port_init     adc.o
    0x0800145c   0x0800145c   0x00000004   Code   RO            3    i.BusFault_Handler  gd32f4xx_it.o
    0x08001460   0x08001460   0x00000002   Code   RO            4    i.DebugMon_Handler  gd32f4xx_it.o
    0x08001462   0x08001462   0x00000004   Code   RO            5    i.HardFault_Handler  gd32f4xx_it.o
    0x08001466   0x08001466   0x00000002   PAD
    0x08001468   0x08001468   0x00000058   Code   RO          774    i.I2C_Start         oled.o
    0x080014c0   0x080014c0   0x00000048   Code   RO          775    i.I2C_Stop          oled.o
    0x08001508   0x08001508   0x00000078   Code   RO          776    i.I2C_WaitAck       oled.o
    0x08001580   0x08001580   0x00000010   Code   RO          777    i.IIC_delay         oled.o
    0x08001590   0x08001590   0x0000001c   Code   RO          741    i.KEY_Init          key.o
    0x080015ac   0x080015ac   0x00000050   Code   RO          296    i.LED_Init          led.o
    0x080015fc   0x080015fc   0x00000004   Code   RO            6    i.MemManage_Handler  gd32f4xx_it.o
    0x08001600   0x08001600   0x00000002   Code   RO            7    i.NMI_Handler       gd32f4xx_it.o
    0x08001602   0x08001602   0x00000002   PAD
    0x08001604   0x08001604   0x00000028   Code   RO          253    i.NVIC_SetPriority  systick.o
    0x0800162c   0x0800162c   0x00000030   Code   RO          778    i.OLED_Clear        oled.o
    0x0800165c   0x0800165c   0x00000060   Code   RO          779    i.OLED_ClearPoint   oled.o
    0x080016bc   0x080016bc   0x0000003c   Code   RO          786    i.OLED_DrawPoint    oled.o
    0x080016f8   0x080016f8   0x00000148   Code   RO          787    i.OLED_Init         oled.o
    0x08001840   0x08001840   0x0000004c   Code   RO          789    i.OLED_Refresh      oled.o
    0x0800188c   0x0800188c   0x000000fc   Code   RO          791    i.OLED_ShowChar     oled.o
    0x08001988   0x08001988   0x00000042   Code   RO          795    i.OLED_ShowString   oled.o
    0x080019ca   0x080019ca   0x00000038   Code   RO          797    i.OLED_WR_Byte      oled.o
    0x08001a02   0x08001a02   0x00000002   Code   RO            8    i.PendSV_Handler    gd32f4xx_it.o
    0x08001a04   0x08001a04   0x00000008   Code   RO            9    i.SDIO_IRQHandler   gd32f4xx_it.o
    0x08001a0c   0x08001a0c   0x00000002   Code   RO           10    i.SVC_Handler       gd32f4xx_it.o
    0x08001a0e   0x08001a0e   0x00000002   PAD
    0x08001a10   0x08001a10   0x0000005c   Code   RO          798    i.Send_Byte         oled.o
    0x08001a6c   0x08001a6c   0x00000008   Code   RO           11    i.SysTick_Handler   gd32f4xx_it.o
    0x08001a74   0x08001a74   0x000000d4   Code   RO         1332    i.SystemInit        system_gd32f4xx.o
    0x08001b48   0x08001b48   0x00000008   Code   RO          953    i.System_Init       function.o
    0x08001b50   0x08001b50   0x000000ec   Code   RO           12    i.USART0_IRQHandler  gd32f4xx_it.o
    0x08001c3c   0x08001c3c   0x00000004   Code   RO           13    i.UsageFault_Handler  gd32f4xx_it.o
    0x08001c40   0x08001c40   0x00000624   Code   RO          954    i.UsrFunction       function.o
    0x08002264   0x08002264   0x00000020   Code   RO         7430    i.__0printf         mc_w.l(printfa.o)
    0x08002284   0x08002284   0x00000028   Code   RO         7432    i.__0sprintf        mc_w.l(printfa.o)
    0x080022ac   0x080022ac   0x00000008   Code   RO         7491    i.__aeabi_errno_addr  mc_w.l(errno.o)
    0x080022b4   0x080022b4   0x00000004   PAD
    0x080022b8   0x080022b8   0x00000038   Code   RO         7170    i.__hardfp_atof     m_wm.l(atof.o)
    0x080022f0   0x080022f0   0x0000000c   Code   RO         7492    i.__read_errno      mc_w.l(errno.o)
    0x080022fc   0x080022fc   0x0000000e   Code   RO         7574    i.__scatterload_copy  mc_w.l(handlers.o)
    0x0800230a   0x0800230a   0x00000002   Code   RO         7575    i.__scatterload_null  mc_w.l(handlers.o)
    0x0800230c   0x0800230c   0x0000000e   Code   RO         7576    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x0800231a   0x0800231a   0x00000002   PAD
    0x0800231c   0x0800231c   0x0000000c   Code   RO         7493    i.__set_errno       mc_w.l(errno.o)
    0x08002328   0x08002328   0x00000184   Code   RO         7437    i._fp_digits        mc_w.l(printfa.o)
    0x080024ac   0x080024ac   0x0000000e   Code   RO         7561    i._is_digit         mc_w.l(scanf_fp.o)
    0x080024ba   0x080024ba   0x00000002   PAD
    0x080024bc   0x080024bc   0x000006dc   Code   RO         7438    i._printf_core      mc_w.l(printfa.o)
    0x08002b98   0x08002b98   0x00000024   Code   RO         7439    i._printf_post_padding  mc_w.l(printfa.o)
    0x08002bbc   0x08002bbc   0x0000002e   Code   RO         7440    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08002bea   0x08002bea   0x0000000a   Code   RO         7442    i._sputc            mc_w.l(printfa.o)
    0x08002bf4   0x08002bf4   0x0000002a   Code   RO         1373    i.adc_calibration_enable  gd32f4xx_adc.o
    0x08002c1e   0x08002c1e   0x00000052   Code   RO         1375    i.adc_channel_length_config  gd32f4xx_adc.o
    0x08002c70   0x08002c70   0x00000024   Code   RO         1376    i.adc_clock_config  gd32f4xx_adc.o
    0x08002c94   0x08002c94   0x00000016   Code   RO         1377    i.adc_data_alignment_config  gd32f4xx_adc.o
    0x08002caa   0x08002caa   0x00000014   Code   RO         1378    i.adc_deinit        gd32f4xx_adc.o
    0x08002cbe   0x08002cbe   0x00000012   Code   RO         1385    i.adc_enable        gd32f4xx_adc.o
    0x08002cd0   0x08002cd0   0x00000034   Code   RO         1387    i.adc_external_trigger_config  gd32f4xx_adc.o
    0x08002d04   0x08002d04   0x00000030   Code   RO         1388    i.adc_external_trigger_source_config  gd32f4xx_adc.o
    0x08002d34   0x08002d34   0x00000008   Code   RO         1389    i.adc_flag_clear    gd32f4xx_adc.o
    0x08002d3c   0x08002d3c   0x0000000e   Code   RO         1390    i.adc_flag_get      gd32f4xx_adc.o
    0x08002d4a   0x08002d4a   0x000000ac   Code   RO         1403    i.adc_routine_channel_config  gd32f4xx_adc.o
    0x08002df6   0x08002df6   0x00000008   Code   RO         1404    i.adc_routine_data_read  gd32f4xx_adc.o
    0x08002dfe   0x08002dfe   0x00000024   Code   RO         1406    i.adc_software_trigger_enable  gd32f4xx_adc.o
    0x08002e22   0x08002e22   0x0000005a   Code   RO         1407    i.adc_special_function_config  gd32f4xx_adc.o
    0x08002e7c   0x08002e7c   0x0000006c   Code   RO          955    i.adjust_sample_cycle  function.o
    0x08002ee8   0x08002ee8   0x00000300   Code   RO          956    i.check_and_create_new_file  function.o
    0x080031e8   0x080031e8   0x000002b4   Code   RO          958    i.check_directories_status  function.o
    0x0800349c   0x0800349c   0x00000090   Code   RO         6912    i.check_fs          ff.o
    0x0800352c   0x0800352c   0x0000006c   Code   RO          959    i.check_key1_press  function.o
    0x08003598   0x08003598   0x000000d4   Code   RO          960    i.check_key234_press  function.o
    0x0800366c   0x0800366c   0x00000014   Code   RO         6913    i.chk_chr           ff.o
    0x08003680   0x08003680   0x00000394   Code   RO         6914    i.chk_mounted       ff.o
    0x08003a14   0x08003a14   0x0000001a   Code   RO         6915    i.clust2sect        ff.o
    0x08003a2e   0x08003a2e   0x00000002   PAD
    0x08003a30   0x08003a30   0x00000030   Code   RO          378    i.cmdsent_error_check  sdcard.o
    0x08003a60   0x08003a60   0x000000d0   Code   RO          961    i.config_read_command  function.o
    0x08003b30   0x08003b30   0x000000b8   Code   RO          962    i.config_save_command  function.o
    0x08003be8   0x08003be8   0x0000001a   Code   RO          963    i.convert_to_bcd    function.o
    0x08003c02   0x08003c02   0x000000ca   Code   RO         6916    i.create_chain      ff.o
    0x08003ccc   0x08003ccc   0x000002a0   Code   RO          964    i.create_directories  function.o
    0x08003f6c   0x08003f6c   0x00000160   Code   RO         6917    i.create_name       ff.o
    0x080040cc   0x080040cc   0x00000014   Code   RO          254    i.delay_1ms         systick.o
    0x080040e0   0x080040e0   0x00000018   Code   RO          255    i.delay_decrement   systick.o
    0x080040f8   0x080040f8   0x0000005c   Code   RO         6918    i.dir_find          ff.o
    0x08004154   0x08004154   0x00000118   Code   RO         6919    i.dir_next          ff.o
    0x0800426c   0x0800426c   0x0000006e   Code   RO         6921    i.dir_register      ff.o
    0x080042da   0x080042da   0x0000009c   Code   RO         6923    i.dir_sdi           ff.o
    0x08004376   0x08004376   0x00000086   Code   RO         6860    i.disk_initialize   diskio.o
    0x080043fc   0x080043fc   0x00000006   Code   RO         6861    i.disk_ioctl        diskio.o
    0x08004402   0x08004402   0x00000050   Code   RO         6862    i.disk_read         diskio.o
    0x08004452   0x08004452   0x0000000c   Code   RO         6863    i.disk_status       diskio.o
    0x0800445e   0x0800445e   0x00000050   Code   RO         6864    i.disk_write        diskio.o
    0x080044ae   0x080044ae   0x00000020   Code   RO         2413    i.dma_channel_disable  gd32f4xx_dma.o
    0x080044ce   0x080044ce   0x00000020   Code   RO         2414    i.dma_channel_enable  gd32f4xx_dma.o
    0x080044ee   0x080044ee   0x00000026   Code   RO         2415    i.dma_channel_subperipheral_select  gd32f4xx_dma.o
    0x08004514   0x08004514   0x000000a6   Code   RO         2418    i.dma_deinit        gd32f4xx_dma.o
    0x080045ba   0x080045ba   0x0000003e   Code   RO         2420    i.dma_flag_clear    gd32f4xx_dma.o
    0x080045f8   0x080045f8   0x0000004c   Code   RO         2421    i.dma_flag_get      gd32f4xx_dma.o
    0x08004644   0x08004644   0x00000040   Code   RO         2422    i.dma_flow_controller_config  gd32f4xx_dma.o
    0x08004684   0x08004684   0x00000164   Code   RO         2431    i.dma_multi_data_mode_init  gd32f4xx_dma.o
    0x080047e8   0x080047e8   0x000000b4   Code   RO          379    i.dma_receive_config  sdcard.o
    0x0800489c   0x0800489c   0x000000b4   Code   RO          380    i.dma_transfer_config  sdcard.o
    0x08004950   0x08004950   0x0000005c   Code   RO          965    i.encode_voltage_hex  function.o
    0x080049ac   0x080049ac   0x00000016   Code   RO         6925    i.f_close           ff.o
    0x080049c2   0x080049c2   0x00000114   Code   RO         6926    i.f_getfree         ff.o
    0x08004ad6   0x08004ad6   0x00000180   Code   RO         6929    i.f_mkdir           ff.o
    0x08004c56   0x08004c56   0x00000002   PAD
    0x08004c58   0x08004c58   0x0000002c   Code   RO         6930    i.f_mount           ff.o
    0x08004c84   0x08004c84   0x0000016c   Code   RO         6931    i.f_open            ff.o
    0x08004df0   0x08004df0   0x0000006e   Code   RO         6932    i.f_opendir         ff.o
    0x08004e5e   0x08004e5e   0x000001ce   Code   RO         6936    i.f_read            ff.o
    0x0800502c   0x0800502c   0x000000b8   Code   RO         6940    i.f_sync            ff.o
    0x080050e4   0x080050e4   0x0000020e   Code   RO         6944    i.f_write           ff.o
    0x080052f2   0x080052f2   0x0000009e   Code   RO         6945    i.follow_path       ff.o
    0x08005390   0x08005390   0x00000024   Code   RO          323    i.fputc             usart.o
    0x080053b4   0x080053b4   0x0000009c   Code   RO          324    i.gd_eval_com_init  usart.o
    0x08005450   0x08005450   0x00000034   Code   RO          968    i.generate_hide_filename  function.o
    0x08005484   0x08005484   0x0000002c   Code   RO          969    i.generate_log_filename  function.o
    0x080054b0   0x080054b0   0x00000038   Code   RO          970    i.generate_overlimit_filename  function.o
    0x080054e8   0x080054e8   0x00000034   Code   RO          971    i.generate_sample_filename  function.o
    0x0800551c   0x0800551c   0x000000b0   Code   RO          972    i.get_current_time  function.o
    0x080055cc   0x080055cc   0x00000054   Code   RO          973    i.get_current_time_string  function.o
    0x08005620   0x08005620   0x00000050   Code   RO          974    i.get_datetime_string  function.o
    0x08005670   0x08005670   0x000000e4   Code   RO         6946    i.get_fat           ff.o
    0x08005754   0x08005754   0x00000004   Code   RO         6865    i.get_fattime       diskio.o
    0x08005758   0x08005758   0x0000008c   Code   RO          975    i.get_unix_timestamp  function.o
    0x080057e4   0x080057e4   0x0000005e   Code   RO         3832    i.gpio_af_set       gd32f4xx_gpio.o
    0x08005842   0x08005842   0x00000004   Code   RO         3833    i.gpio_bit_reset    gd32f4xx_gpio.o
    0x08005846   0x08005846   0x00000004   Code   RO         3834    i.gpio_bit_set      gd32f4xx_gpio.o
    0x0800584a   0x0800584a   0x00000004   Code   RO         3835    i.gpio_bit_toggle   gd32f4xx_gpio.o
    0x0800584e   0x0800584e   0x00000002   PAD
    0x08005850   0x08005850   0x00000074   Code   RO          381    i.gpio_config       sdcard.o
    0x080058c4   0x080058c4   0x00000010   Code   RO         3838    i.gpio_input_bit_get  gd32f4xx_gpio.o
    0x080058d4   0x080058d4   0x0000004e   Code   RO         3840    i.gpio_mode_set     gd32f4xx_gpio.o
    0x08005922   0x08005922   0x00000042   Code   RO         3842    i.gpio_output_options_set  gd32f4xx_gpio.o
    0x08005964   0x08005964   0x000000b4   Code   RO          976    i.hide_command      function.o
    0x08005a18   0x08005a18   0x00000034   Code   RO          977    i.increment_boot_count  function.o
    0x08005a4c   0x08005a4c   0x00000160   Code   RO          978    i.init_data_storage  function.o
    0x08005bac   0x08005bac   0x00000148   Code   RO          979    i.load_config_from_flash  function.o
    0x08005cf4   0x08005cf4   0x00000134   Code   RO          980    i.local_rtc_init    function.o
    0x08005e28   0x08005e28   0x0000000e   Code   RO          229    i.main              main.o
    0x08005e36   0x08005e36   0x00000026   Code   RO         6948    i.mem_cmp           ff.o
    0x08005e5c   0x08005e5c   0x0000001a   Code   RO         6949    i.mem_cpy           ff.o
    0x08005e76   0x08005e76   0x00000014   Code   RO         6950    i.mem_set           ff.o
    0x08005e8a   0x08005e8a   0x00000072   Code   RO         6951    i.move_window       ff.o
    0x08005efc   0x08005efc   0x00000020   Code   RO          982    i.nvic_config       function.o
    0x08005f1c   0x08005f1c   0x000000c4   Code   RO         4405    i.nvic_irq_enable   gd32f4xx_misc.o
    0x08005fe0   0x08005fe0   0x00000014   Code   RO         4406    i.nvic_priority_group_set  gd32f4xx_misc.o
    0x08005ff4   0x08005ff4   0x000001d0   Code   RO          983    i.parse_time_input  function.o
    0x080061c4   0x080061c4   0x00000014   Code   RO         4462    i.pmu_backup_write_enable  gd32f4xx_pmu.o
    0x080061d8   0x080061d8   0x00000136   Code   RO         6952    i.put_fat           ff.o
    0x0800630e   0x0800630e   0x00000002   PAD
    0x08006310   0x08006310   0x00000084   Code   RO          382    i.r1_error_check    sdcard.o
    0x08006394   0x08006394   0x000000ae   Code   RO          383    i.r1_error_type_check  sdcard.o
    0x08006442   0x08006442   0x00000002   PAD
    0x08006444   0x08006444   0x00000050   Code   RO          384    i.r2_error_check    sdcard.o
    0x08006494   0x08006494   0x0000003c   Code   RO          385    i.r3_error_check    sdcard.o
    0x080064d0   0x080064d0   0x000000a8   Code   RO          386    i.r6_error_check    sdcard.o
    0x08006578   0x08006578   0x00000050   Code   RO          387    i.r7_error_check    sdcard.o
    0x080065c8   0x080065c8   0x00000014   Code   RO         4605    i.rcu_bkp_reset_disable  gd32f4xx_rcu.o
    0x080065dc   0x080065dc   0x00000014   Code   RO         4606    i.rcu_bkp_reset_enable  gd32f4xx_rcu.o
    0x080065f0   0x080065f0   0x00000124   Code   RO         4610    i.rcu_clock_freq_get  gd32f4xx_rcu.o
    0x08006714   0x08006714   0x00000024   Code   RO          388    i.rcu_config        sdcard.o
    0x08006738   0x08006738   0x00000024   Code   RO         4613    i.rcu_flag_get      gd32f4xx_rcu.o
    0x0800675c   0x0800675c   0x00000024   Code   RO         4626    i.rcu_osci_on       gd32f4xx_rcu.o
    0x08006780   0x08006780   0x0000015c   Code   RO         4627    i.rcu_osci_stab_wait  gd32f4xx_rcu.o
    0x080068dc   0x080068dc   0x00000024   Code   RO         4629    i.rcu_periph_clock_enable  gd32f4xx_rcu.o
    0x08006900   0x08006900   0x00000024   Code   RO         4632    i.rcu_periph_reset_disable  gd32f4xx_rcu.o
    0x08006924   0x08006924   0x00000024   Code   RO         4633    i.rcu_periph_reset_enable  gd32f4xx_rcu.o
    0x08006948   0x08006948   0x00000018   Code   RO         4638    i.rcu_rtc_clock_config  gd32f4xx_rcu.o
    0x08006960   0x08006960   0x00000008   Code   RO          985    i.read_adc_voltage  function.o
    0x08006968   0x08006968   0x000004e8   Code   RO          986    i.read_config_from_tf  function.o
    0x08006e50   0x08006e50   0x00000068   Code   RO         6953    i.remove_chain      ff.o
    0x08006eb8   0x08006eb8   0x00000320   Code   RO          987    i.rtc_config_command  function.o
    0x080071d8   0x080071d8   0x00000064   Code   RO         4910    i.rtc_current_time_get  gd32f4xx_rtc.o
    0x0800723c   0x0800723c   0x000000dc   Code   RO         4915    i.rtc_init          gd32f4xx_rtc.o
    0x08007318   0x08007318   0x00000048   Code   RO         4916    i.rtc_init_mode_enter  gd32f4xx_rtc.o
    0x08007360   0x08007360   0x00000014   Code   RO         4917    i.rtc_init_mode_exit  gd32f4xx_rtc.o
    0x08007374   0x08007374   0x0000005c   Code   RO          988    i.rtc_now_command   function.o
    0x080073d0   0x080073d0   0x00000060   Code   RO         4922    i.rtc_register_sync_wait  gd32f4xx_rtc.o
    0x08007430   0x08007430   0x0000001c   Code   RO          991    i.save_config_to_flash  function.o
    0x0800744c   0x0800744c   0x00000218   Code   RO          389    i.sd_block_read     sdcard.o
    0x08007664   0x08007664   0x00000320   Code   RO          390    i.sd_block_write    sdcard.o
    0x08007984   0x08007984   0x00000094   Code   RO          391    i.sd_bus_mode_config  sdcard.o
    0x08007a18   0x08007a18   0x000000fc   Code   RO          392    i.sd_bus_width_config  sdcard.o
    0x08007b14   0x08007b14   0x000002c0   Code   RO          394    i.sd_card_information_get  sdcard.o
    0x08007dd4   0x08007dd4   0x0000011c   Code   RO          395    i.sd_card_init      sdcard.o
    0x08007ef0   0x08007ef0   0x00000026   Code   RO          396    i.sd_card_select_deselect  sdcard.o
    0x08007f16   0x08007f16   0x00000002   PAD
    0x08007f18   0x08007f18   0x000000b8   Code   RO          397    i.sd_card_state_get  sdcard.o
    0x08007fd0   0x08007fd0   0x00000048   Code   RO          398    i.sd_cardstatus_get  sdcard.o
    0x08008018   0x08008018   0x00000018   Code   RO          399    i.sd_datablocksize_get  sdcard.o
    0x08008030   0x08008030   0x00000046   Code   RO          401    i.sd_init           sdcard.o
    0x08008076   0x08008076   0x00000002   PAD
    0x08008078   0x08008078   0x00000130   Code   RO          402    i.sd_interrupts_process  sdcard.o
    0x080081a8   0x080081a8   0x0000029c   Code   RO          404    i.sd_multiblocks_read  sdcard.o
    0x08008444   0x08008444   0x00000398   Code   RO          405    i.sd_multiblocks_write  sdcard.o
    0x080087dc   0x080087dc   0x0000012c   Code   RO          407    i.sd_power_on       sdcard.o
    0x08008908   0x08008908   0x0000015c   Code   RO          408    i.sd_scr_get        sdcard.o
    0x08008a64   0x08008a64   0x00000018   Code   RO          410    i.sd_transfer_mode_config  sdcard.o
    0x08008a7c   0x08008a7c   0x00000024   Code   RO          412    i.sd_transfer_stop  sdcard.o
    0x08008aa0   0x08008aa0   0x0000001c   Code   RO         5163    i.sdio_bus_mode_set  gd32f4xx_sdio.o
    0x08008abc   0x08008abc   0x00000034   Code   RO         5170    i.sdio_clock_config  gd32f4xx_sdio.o
    0x08008af0   0x08008af0   0x00000014   Code   RO         5172    i.sdio_clock_enable  gd32f4xx_sdio.o
    0x08008b04   0x08008b04   0x0000000c   Code   RO         5173    i.sdio_command_index_get  gd32f4xx_sdio.o
    0x08008b10   0x08008b10   0x00000038   Code   RO         5174    i.sdio_command_response_config  gd32f4xx_sdio.o
    0x08008b48   0x08008b48   0x00000014   Code   RO         5176    i.sdio_csm_enable   gd32f4xx_sdio.o
    0x08008b5c   0x08008b5c   0x0000003c   Code   RO         5177    i.sdio_data_config  gd32f4xx_sdio.o
    0x08008b98   0x08008b98   0x0000000c   Code   RO         5179    i.sdio_data_read    gd32f4xx_sdio.o
    0x08008ba4   0x08008ba4   0x0000001c   Code   RO         5180    i.sdio_data_transfer_config  gd32f4xx_sdio.o
    0x08008bc0   0x08008bc0   0x0000000c   Code   RO         5181    i.sdio_data_write   gd32f4xx_sdio.o
    0x08008bcc   0x08008bcc   0x00000014   Code   RO         5182    i.sdio_deinit       gd32f4xx_sdio.o
    0x08008be0   0x08008be0   0x00000014   Code   RO         5183    i.sdio_dma_disable  gd32f4xx_sdio.o
    0x08008bf4   0x08008bf4   0x00000014   Code   RO         5184    i.sdio_dma_enable   gd32f4xx_sdio.o
    0x08008c08   0x08008c08   0x00000014   Code   RO         5185    i.sdio_dsm_disable  gd32f4xx_sdio.o
    0x08008c1c   0x08008c1c   0x00000014   Code   RO         5186    i.sdio_dsm_enable   gd32f4xx_sdio.o
    0x08008c30   0x08008c30   0x0000000c   Code   RO         5188    i.sdio_flag_clear   gd32f4xx_sdio.o
    0x08008c3c   0x08008c3c   0x00000014   Code   RO         5189    i.sdio_flag_get     gd32f4xx_sdio.o
    0x08008c50   0x08008c50   0x00000014   Code   RO         5190    i.sdio_hardware_clock_disable  gd32f4xx_sdio.o
    0x08008c64   0x08008c64   0x00000010   Code   RO         5192    i.sdio_interrupt_disable  gd32f4xx_sdio.o
    0x08008c74   0x08008c74   0x00000010   Code   RO         5193    i.sdio_interrupt_enable  gd32f4xx_sdio.o
    0x08008c84   0x08008c84   0x0000000c   Code   RO         5194    i.sdio_interrupt_flag_clear  gd32f4xx_sdio.o
    0x08008c90   0x08008c90   0x00000014   Code   RO         5195    i.sdio_interrupt_flag_get  gd32f4xx_sdio.o
    0x08008ca4   0x08008ca4   0x0000000c   Code   RO         5198    i.sdio_power_state_get  gd32f4xx_sdio.o
    0x08008cb0   0x08008cb0   0x0000000c   Code   RO         5199    i.sdio_power_state_set  gd32f4xx_sdio.o
    0x08008cbc   0x08008cbc   0x0000003c   Code   RO         5203    i.sdio_response_get  gd32f4xx_sdio.o
    0x08008cf8   0x08008cf8   0x0000001c   Code   RO         5208    i.sdio_wait_type_set  gd32f4xx_sdio.o
    0x08008d14   0x08008d14   0x000001f8   Code   RO          992    i.set_limit_value   function.o
    0x08008f0c   0x08008f0c   0x000001e8   Code   RO          993    i.set_ratio_value   function.o
    0x080090f4   0x080090f4   0x0000000a   Code   RO         5469    i.spi_enable        gd32f4xx_spi.o
    0x080090fe   0x080090fe   0x00000002   PAD
    0x08009100   0x08009100   0x00000054   Code   RO          597    i.spi_flash_buffer_read  spi_flash.o
    0x08009154   0x08009154   0x00000122   Code   RO          598    i.spi_flash_buffer_write  spi_flash.o
    0x08009276   0x08009276   0x00000002   PAD
    0x08009278   0x08009278   0x0000009c   Code   RO          600    i.spi_flash_init    spi_flash.o
    0x08009314   0x08009314   0x0000005c   Code   RO          601    i.spi_flash_page_write  spi_flash.o
    0x08009370   0x08009370   0x00000054   Code   RO          603    i.spi_flash_read_id  spi_flash.o
    0x080093c4   0x080093c4   0x00000044   Code   RO          604    i.spi_flash_sector_erase  spi_flash.o
    0x08009408   0x08009408   0x00000038   Code   RO          605    i.spi_flash_send_byte  spi_flash.o
    0x08009440   0x08009440   0x00000038   Code   RO          608    i.spi_flash_wait_for_write_end  spi_flash.o
    0x08009478   0x08009478   0x00000024   Code   RO          609    i.spi_flash_write_enable  spi_flash.o
    0x0800949c   0x0800949c   0x00000008   Code   RO         5471    i.spi_i2s_data_receive  gd32f4xx_spi.o
    0x080094a4   0x080094a4   0x00000004   Code   RO         5472    i.spi_i2s_data_transmit  gd32f4xx_spi.o
    0x080094a8   0x080094a8   0x00000010   Code   RO         5474    i.spi_i2s_flag_get  gd32f4xx_spi.o
    0x080094b8   0x080094b8   0x00000032   Code   RO         5479    i.spi_init          gd32f4xx_spi.o
    0x080094ea   0x080094ea   0x00000002   PAD
    0x080094ec   0x080094ec   0x00000088   Code   RO          994    i.start_sampling    function.o
    0x08009574   0x08009574   0x000000d8   Code   RO          995    i.stop_sampling     function.o
    0x0800964c   0x0800964c   0x000000ca   Code   RO         6954    i.sync              ff.o
    0x08009716   0x08009716   0x00000002   PAD
    0x08009718   0x08009718   0x00000108   Code   RO         1333    i.system_clock_240m_25m_hxtal  system_gd32f4xx.o
    0x08009820   0x08009820   0x00000008   Code   RO         1334    i.system_clock_config  system_gd32f4xx.o
    0x08009828   0x08009828   0x000001e4   Code   RO          996    i.system_self_check  function.o
    0x08009a0c   0x08009a0c   0x00000050   Code   RO          256    i.systick_config    systick.o
    0x08009a5c   0x08009a5c   0x00000018   Code   RO         1000    i.toggle_sampling_by_key  function.o
    0x08009a74   0x08009a74   0x000000bc   Code   RO         1001    i.unhide_command    function.o
    0x08009b30   0x08009b30   0x000000b0   Code   RO         1002    i.update_oled_display  function.o
    0x08009be0   0x08009be0   0x000002f0   Code   RO         1003    i.update_sampling   function.o
    0x08009ed0   0x08009ed0   0x000000e8   Code   RO         6454    i.usart_baudrate_set  gd32f4xx_usart.o
    0x08009fb8   0x08009fb8   0x00000014   Code   RO          325    i.usart_clear_buffer  usart.o
    0x08009fcc   0x08009fcc   0x0000000a   Code   RO         6458    i.usart_data_receive  gd32f4xx_usart.o
    0x08009fd6   0x08009fd6   0x00000008   Code   RO         6459    i.usart_data_transmit  gd32f4xx_usart.o
    0x08009fde   0x08009fde   0x00000002   PAD
    0x08009fe0   0x08009fe0   0x000000dc   Code   RO         6460    i.usart_deinit      gd32f4xx_usart.o
    0x0800a0bc   0x0800a0bc   0x0000000a   Code   RO         6464    i.usart_enable      gd32f4xx_usart.o
    0x0800a0c6   0x0800a0c6   0x00000034   Code   RO         6465    i.usart_flag_clear  gd32f4xx_usart.o
    0x0800a0fa   0x0800a0fa   0x0000001e   Code   RO         6466    i.usart_flag_get    gd32f4xx_usart.o
    0x0800a118   0x0800a118   0x00000058   Code   RO          326    i.usart_get_command  usart.o
    0x0800a170   0x0800a170   0x0000001a   Code   RO         6474    i.usart_interrupt_enable  gd32f4xx_usart.o
    0x0800a18a   0x0800a18a   0x00000002   PAD
    0x0800a18c   0x0800a18c   0x0000000c   Code   RO          327    i.usart_is_command_ready  usart.o
    0x0800a198   0x0800a198   0x00000010   Code   RO         6491    i.usart_receive_config  gd32f4xx_usart.o
    0x0800a1a8   0x0800a1a8   0x00000010   Code   RO         6506    i.usart_transmit_config  gd32f4xx_usart.o
    0x0800a1b8   0x0800a1b8   0x0000002a   Code   RO         6955    i.validate          ff.o
    0x0800a1e2   0x0800a1e2   0x00000002   PAD
    0x0800a1e4   0x0800a1e4   0x00000344   Code   RO         1005    i.write_hide_data   function.o
    0x0800a528   0x0800a528   0x0000009c   Code   RO         1006    i.write_log_data    function.o
    0x0800a5c4   0x0800a5c4   0x00000268   Code   RO         1007    i.write_overlimit_data  function.o
    0x0800a82c   0x0800a82c   0x0000020c   Code   RO         1008    i.write_sample_data  function.o
    0x0800aa38   0x0800aa38   0x00001fd8   Data   RO          800    .constdata          oled.o
    0x0800ca10   0x0800ca10   0x00000081   Data   RO         7530    .constdata          mc_w.l(ctype_o.o)
    0x0800ca91   0x0800ca91   0x00000003   PAD
    0x0800ca94   0x0800ca94   0x00000004   Data   RO         7531    .constdata          mc_w.l(ctype_o.o)
    0x0800ca98   0x0800ca98   0x000000ae   Data   RO         1010    .conststring        function.o
    0x0800cb46   0x0800cb46   0x00000002   PAD
    0x0800cb48   0x0800cb48   0x00000020   Data   RO         7572    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800cb68, Size: 0x00001720, Max: 0x00030000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0800cb68   0x00000004   Data   RW          257    .data               systick.o
    0x20000004   0x0800cb6c   0x00000002   Data   RW          329    .data               usart.o
    0x20000006   0x0800cb6e   0x00000002   PAD
    0x20000008   0x0800cb70   0x00000024   Data   RW          414    .data               sdcard.o
    0x2000002c   0x0800cb94   0x00000050   Data   RW         1011    .data               function.o
    0x2000007c   0x0800cbe4   0x00000004   Data   RW         1335    .data               system_gd32f4xx.o
    0x20000080   0x0800cbe8   0x00000006   Data   RW         6956    .data               ff.o
    0x20000086   0x0800cbee   0x00000002   PAD
    0x20000088   0x0800cbf0   0x00000004   Data   RW         7486    .data               mc_w.l(stdout.o)
    0x2000008c   0x0800cbf4   0x00000004   Data   RW         7494    .data               mc_w.l(errno.o)
    0x20000090        -       0x00000040   Zero   RW          328    .bss                usart.o
    0x200000d0        -       0x00000020   Zero   RW          413    .bss                sdcard.o
    0x200000f0        -       0x00000240   Zero   RW          799    .bss                oled.o
    0x20000330        -       0x00000fec   Zero   RW         1009    .bss                function.o
    0x2000131c   0x0800cbf8   0x00000004   PAD
    0x20001320        -       0x00000400   Zero   RW         6851    STACK               startup_gd32f450_470.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       268         26          0          0          0       2279   adc.o
       316          0          0          0          0       4784   diskio.o
      5912         46          0          6          0      30684   ff.o
     14334       6952        174         80       4076      41194   function.o
       648          8          0          0          0      10010   gd32f4xx_adc.o
       826          4          0          0          0       6619   gd32f4xx_dma.o
       266          0          0          0          0       5291   gd32f4xx_gpio.o
       276         62          0          0          0      79273   gd32f4xx_it.o
       216         20          0          0          0       1576   gd32f4xx_misc.o
        20          6          0          0          0        594   gd32f4xx_pmu.o
       884         72          0          0          0       8056   gd32f4xx_rcu.o
       508         24          0          0          0       4558   gd32f4xx_rtc.o
       628        136          0          0          0      16782   gd32f4xx_sdio.o
        88          0          0          0          0       4114   gd32f4xx_spi.o
       620         18          0          0          0       7222   gd32f4xx_usart.o
        28          4          0          0          0        503   key.o
        80          4          0          0          0        539   led.o
        14          0          0          0          0        515   main.o
      1370         58       8152          0        576      10018   oled.o
      6966        344          0         36         32      32236   sdcard.o
       922         50          0          0          0       7040   spi_flash.o
        36          8        428          0       1024        904   startup_gd32f450_470.o
       484         32          0          4          0       3051   system_gd32f4xx.o
       164         24          0          4          0      29266   systick.o
       312         40          0          2         64       5218   usart.o

    ----------------------------------------------------------------------
     36220       <USER>       <GROUP>        136       5776     312326   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        34          0          2          4          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        56         12          0          0          0        132   atof.o
        56          8          0          0          0         84   __0sscanf.o
        28          0          0          0          0         68   _chval.o
       812          4          0          0          0        112   _scanf.o
       332          0          0          0          0         96   _scanf_int.o
        64          0          0          0          0         84   _sgetc.o
       158          0          0          0          0         92   _strtoul.o
        26          0          0          0          0         80   atoi.o
         8          4        133          0          0         68   ctype_o.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        32         16          0          4          0        204   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
        18          0          0          0          0         76   isspace_o.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0        108   memseta.o
      2308         96          0          0          0        604   printfa.o
        40          8          0          0          0         84   scanf_char.o
       878         12          0          0          0        216   scanf_fp.o
         0          0          0          4          0          0   stdout.o
        20          0          0          0          0         68   strchr.o
        28          0          0          0          0         76   strcmp.o
        14          0          0          0          0         68   strlen.o
        24          0          0          0          0         76   strncpy.o
        36          0          0          0          0         80   strstr.o
       156         12          0          0          0        120   strtod.o
       112          0          0          0          0         88   strtol.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdcmple.o
        48          0          0          0          0         68   cdrcmple.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
        24          0          0          0          0         76   dfltul.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o
       110          0          0          0          0        168   fepilogue.o

    ----------------------------------------------------------------------
      6928        <USER>        <GROUP>          8          0       4252   Library Totals
        18          4          3          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

        56         12          0          0          0        132   m_wm.l
      5512        176        133          8          0       2996   mc_w.l
      1342          0          0          0          0       1124   mf_w.l

    ----------------------------------------------------------------------
      6928        <USER>        <GROUP>          8          0       4252   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     43148       8130       8924        144       5776     290146   Grand Totals
     43148       8130       8924        144       5776     290146   ELF Image Totals
     43148       8130       8924        144          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                52072 (  50.85kB)
    Total RW  Size (RW Data + ZI Data)              5920 (   5.78kB)
    Total ROM Size (Code + RO Data + RW Data)      52216 (  50.99kB)

==============================================================================

