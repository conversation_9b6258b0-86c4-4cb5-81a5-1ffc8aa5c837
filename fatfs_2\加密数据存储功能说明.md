# 加密数据存储功能实现说明

## 功能概述
已成功实现加密数据存储功能，当启用hide模式时，将采样数据以未加密+加密的双行格式存储到TF卡的hideData文件夹中。

## 功能特性

### 1. 加密模式控制
**启用命令：** `hide`
- 仅在采样运行时可启用
- 启用后数据格式切换为HEX模式显示
- 串口输出：`<=== hide`

**禁用命令：** `unhide`
- 仅在采样运行时可禁用
- 禁用后恢复正常数据格式显示
- 串口输出：`<=== unhide`

### 2. 文件存储要求
**a. 文件数量控制：**
- 每个文件存储10条数据（每条数据包含2行：未加密+加密）
- 超过10条后自动创建新文件

**b. 文件命名格式：**
- 文件名：`hideData{datetime}.txt`
- datetime格式：连续14位数字（YYYYMMDDHHMMSS）
- 示例：`hideData20250101003010.txt`（对应2025-01-01 00:30:10）

**c. 文件内容格式：**
```
2025-01-01 00:30:10 1.5V
hide: XXXXXXXXXXXXXXXX
2025-01-01 00:30:13 1.5V
hide: XXXXXXXXXXXXXXXX
2025-01-01 00:30:16 1.5V
hide: XXXXXXXXXXXXXXXX
2025-01-01 00:30:19 1.5V
hide: XXXXXXXXXXXXXXXX
2025-01-01 00:30:21 1.5V
hide: XXXXXXXXXXXXXXXX
```

### 3. 存储路径
- 目录：`0:/hideData/`
- 自动创建目录（如果不存在）

### 4. 存储逻辑
**启用加密存储时：**
- ✅ hideData文件夹：存储未加密+加密数据
- ❌ sample文件夹：不存储数据
- ✅ overLimit文件夹：超阈值时仍按原格式存储

**正常模式时：**
- ✅ sample文件夹：存储正常采样数据
- ❌ hideData文件夹：不存储数据
- ✅ overLimit文件夹：超阈值时按原格式存储

## 技术实现

### 核心函数

#### write_hide_data()
```c
void write_hide_data(char* time_str, float voltage)
```
- **功能：** 写入加密数据到hideData文件夹
- **参数：**
  - `time_str`: 时间字符串（格式：2025-01-01 00:30:10）
  - `voltage`: 当前电压值
- **格式：** 每条记录包含两行
  1. 未加密：`时间 电压V`
  2. 加密：`hide: 16位十六进制`

#### 加密算法
- **时间戳：** Unix时间戳转换为8位十六进制
- **电压编码：** 浮点电压值转换为8位十六进制
- **最终格式：** `hide: TTTTTTTTXXXXXXXX`（T=时间戳，X=电压值）

### 数据格式转换
- **未加密数据：** `2025-01-01 00:30:10 1.5V`
- **加密数据：** `hide: 678901234567ABCD`（示例）
- **校验功能：** 同时存储两种格式便于验证准确性

### 文件管理
- **计数器：** `file_manager.hide_count`
- **文件句柄：** `file_manager.hide_file`
- **自动切换：** 达到10条记录时自动创建新文件

## 集成说明

### 与采样系统集成
- 在每次采样时检查hide_mode状态
- hide模式时：
  1. 串口输出HEX格式数据
  2. 写入hideData文件（双行格式）
  3. 不写入sample文件
  4. 超限时仍写入overLimit文件

### 与其他存储功能的关系
- **互斥存储：** hide模式与sample模式互斥
- **独立超限：** overLimit数据独立于hide/sample模式
- **统一管理：** 使用相同的文件管理器结构

## 调试功能
添加了详细的调试输出：
- 文件创建信息
- 未加密数据写入确认
- 加密数据写入确认
- 计数器状态
- 错误信息

## 使用示例

### 启动采样并启用加密
```bash
start
Periodic Sampling
sample cycle: 5s

hide
<=== hide
Data format switched to HEX mode
```

### 串口输出（HEX格式）
```bash
=== >678901234567ABCD
=== >678901234567ABCE
=== >678901234567ABCF
```

### 文件内容（hideData文件夹）
```
2025-01-01 12:00:05 10.5V
hide: 678901234567ABCD
2025-01-01 12:00:10 10.6V
hide: 678901234567ABCE
2025-01-01 12:00:15 10.7V
hide: 678901234567ABCF
```

### 禁用加密模式
```bash
unhide
<=== unhide
Data format restored to normal mode
```

## 文件结构示例
```
TF卡根目录/
├── sample/
│   └── （hide模式时为空）
├── overLimit/
│   └── overLimit20250101120005.txt
├── hideData/
│   └── hideData20250101120005.txt
└── log/
    └── log1.txt
```

## 验证方法

### 1. 目录检查
```bash
check dirs
=== Directory Status Check ===
hideData directory: EXISTS
Storage enabled: YES
Hide count: 5
```

### 2. 功能测试
1. 启动采样：`start`
2. 启用加密：`hide`
3. 观察串口输出为HEX格式
4. 检查hideData文件夹中的文件
5. 验证文件内容包含未加密+加密双行格式
6. 禁用加密：`unhide`
7. 确认恢复正常格式

### 3. 超限测试
1. 设置较低limit值
2. 在hide模式下触发超限
3. 确认overLimit文件夹仍有数据
4. 确认sample文件夹无数据

---
**实现完成时间：** 2025-06-16  
**状态：** ✅ 已实现并测试  
**符合要求：** ✅ 完全符合用户规格要求
