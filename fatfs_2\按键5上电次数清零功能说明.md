# 按键5上电次数清零功能说明

## 功能概述

本功能实现了通过按键5（KEY5）控制系统上电次数计数器清零的能力，提供硬件按键和串口命令两种清零方式。

## 功能特性

### 1. 按键控制
- **KEY5**: 按下清零上电次数计数器
- **硬件引脚**: GPIOE PIN13
- **触发方式**: 下降沿触发（按下时触发）
- **防抖处理**: 内置20ms防抖机制

### 2. 串口命令
- **命令**: `clear boot`
- **功能**: 通过串口命令清零上电次数
- **输出**: 显示清零后的计数值

### 3. 输出格式
按键或命令执行后，系统输出：
```
=== >Boot count cleared: 0
```

### 4. 数据持久化
- 清零操作立即保存到SPI Flash
- 断电重启后计数保持为0
- 自动记录操作日志

## 技术实现

### 硬件配置
```c
// KEY5引脚定义
#define KEY5_PIN        GPIO_PIN_13
#define KEY_PORT        GPIOE

// 按键状态变量
uint8_t key5_last_state = 0;     // KEY5上次状态
uint8_t key5_pressed_flag = 0;   // KEY5按下标志
```

### 按键检测机制
- 采用边沿触发检测（下降沿）
- 内置防抖处理，避免误触发
- 独立状态标志，防止重复触发

### 核心函数
```c
// 检查KEY5按键状态
void check_key5_press(void);

// 清零上电次数
void clear_boot_count(void);
```

## 使用方法

### 方法1：硬件按键
1. 按下KEY5按键
2. 系统自动检测按键状态
3. 执行清零操作并显示结果

### 方法2：串口命令
1. 通过串口发送命令：`clear boot`
2. 系统执行清零操作
3. 显示清零结果

## 系统集成

### 主循环检测
```c
// 在主循环中检测按键状态
if(key_check_counter >= 1000) {
    check_key1_press();
    check_key234_press();
    check_key5_press();  // 新增KEY5检测
    key_check_counter = 0;
}
```

### 命令系统集成
- 主命令循环支持 `clear boot` 命令
- process_command函数支持命令处理
- 帮助信息已更新

## 日志记录

清零操作会自动记录到系统日志：
```
Boot count cleared by KEY5
```

## 验证方法

1. **查看当前上电次数**：
   - 使用 `config read` 命令查看当前boot_count值
   - 或使用 `test` 命令在系统自检中查看

2. **执行清零操作**：
   - 按下KEY5按键，或发送 `clear boot` 命令

3. **验证清零结果**：
   - 再次使用 `config read` 命令确认boot_count为0
   - 重启系统后验证计数从1开始重新计数

## 注意事项

1. **防误操作**: 按键具有防抖机制，避免误触发
2. **即时保存**: 清零操作立即保存到Flash，确保数据持久性
3. **日志记录**: 所有清零操作都会记录到系统日志
4. **双重方式**: 提供按键和串口两种清零方式，增加操作灵活性

## 相关命令总结

- `config read` - 查看当前上电次数
- `clear boot` - 串口命令清零上电次数
- `test` - 系统自检（显示当前上电次数）
- KEY5按键 - 硬件按键清零上电次数
